# -*- mode: python ; coding: utf-8 -*-
import os
import sys
from PyInstaller.utils.hooks import collect_data_files


# src içerisindeki tüm klasörleri ve dosyaları dahil et
src_dirs = ['src/chatbot', 'src/agent_graph', 'src/utils']

# <PERSON><PERSON><PERSON> `chromadb`'yi ve diğer bağımlılıkları ekleyelim
site_packages = os.path.join(sys.prefix, 'Lib', 'site-packages')

# `chromadb`'nin yeri
chromadb_path = os.path.join(site_packages, 'chromadb')


datas = []
for src_dir in src_dirs:
    for root, _, files in os.walk(src_dir):
        for file in files:
            full_path = os.path.join(root, file)
            relative_path = os.path.relpath(root, 'src')
            datas.append((full_path, os.path.join('src', relative_path)))

# <PERSON><PERSON><PERSON> `chromadb`'yi bulursa, ve<PERSON><PERSON><PERSON> e<PERSON>
if os.path.exists(chromadb_path):
    datas += collect_data_files('chromadb')

datas += [
    (os.path.join('configs', 'tools_config.yml'), 'dist/configs/tools_config.yml'),
    (os.path.join('configs', 'project_config.yml'), 'dist/configs/project_config.yml'),
    (os.path.join('data', 'is_guvenligi_vectordb'), 'dist/data/is_guvenligi_vectordb'),
    (os.path.join('data', 'is_sagligi_vectordb'), 'dist/data/is_sagligi_vectordb'),
    (os.path.join('data', 'senato_kararlari_vectordb'), 'dist/data/senato_kararlari_vectordb'),
    (os.path.join('data', 'usul_ve_esaslar_vectordb'), 'dist/data/usul_ve_esaslar_vectordb'),
    (os.path.join('data', 'yok_yazilari_vectordb'), 'dist/data/yok_yazilari_vectordb'),
    (os.path.join('data', 'yonetmelikler_vectordb'), 'dist/data/yonetmelikler_vectordb'),
    (os.path.join('.env'), 'dist/.env'),
]

a = Analysis(
    ['src/api.py'],  # api.py artık src klasörünün içinde
    pathex=['src'],  # src klasörünü Python modülleri için ekleyelim
    binaries=[],
    datas=datas,
    hiddenimports=[
        'chromadb',
        'chromadb.db',
        'chromadb.db.impl',
        'chromadb.db.impl.sqlite',
        'chromadb.segment.impl.manager',
        'chromadb.segment.impl.manager.local',
        'chromadb.segment.impl.metadata',
        'chromadb.segment.impl.metadata.sqlite',
        'chromadb.migrations',
        'chromadb.migrations.embeddings_queue',
        'chromadb.telemetry',
        'chromadb.telemetry.product.posthog',
        'chromadb.utils.embedding_functions',
        'chromadb.utils.embedding_functions.onnx_mini_lm_l6_v2',
        'chromadb.utils.embedding_functions.onnx_mini_lm_l6_v2.ONNXMiniLM_L6_V2',
        'chromadb.api.models.Collection',
        'chromadb.api.models.CollectionCommon',
        'chromadb.api.segment',
        'chromadb.execution.executor.local',
        'chromadb.quota.simple_quota_enforcer',
        'chromadb.rate_limit.simple_rate_limit',
        'posthog',
        'langchain_chroma',
        'langchain_chroma.vectorstores',
        'onnxruntime',
        'onnxruntime.capi.onnxruntime_validation',
        'hnswlib',
        'pypika',
        'pymongo',
        'sqlalchemy',
        'sqlalchemy.orm',
        'pysqlite2',
        'MySQL-python'
        'transformers',
        'tiktoken',
        'tiktoken_ext.openai_public',
        'tiktoken_ext',
        'anyio.streams',
        'pydantic',
        'pydantic_core',
        'pydantic.deprecated.decorator',
        
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='api',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
