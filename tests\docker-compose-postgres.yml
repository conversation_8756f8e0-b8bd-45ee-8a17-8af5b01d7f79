version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: atlas_postgres_test
    environment:
      POSTGRES_DB: university_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: admin123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d university_db"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
