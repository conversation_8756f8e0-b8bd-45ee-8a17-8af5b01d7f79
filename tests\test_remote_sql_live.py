#!/usr/bin/env python3
"""
Test script for remote SQL database connections with live API testing.
This script tests the AdminBot's SQL functionality with different connection scenarios.
"""

import asyncio
import sys
import os
import logging
import requests
import json
import time
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API base URL
API_BASE_URL = "http://localhost:8000"


def test_api_connection():
    """Test if the API is running."""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            logger.info("✅ API is running and accessible")
            return True
        else:
            logger.error(f"❌ API returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Cannot connect to API: {str(e)}")
        logger.error("Make sure the API is running with: python -m app.main")
        return False


def get_bot_info(bot_name):
    """Get information about a specific bot."""
    try:
        response = requests.get(f"{API_BASE_URL}/bots/{bot_name}", timeout=10)
        if response.status_code == 200:
            bot_info = response.json()
            logger.info(f"✅ {bot_name} is available")
            logger.info(f"Description: {bot_info.get('description', 'N/A')}")

            # Show enabled tools
            tools = bot_info.get("tools", [])
            enabled_tools = []
            if isinstance(tools, list):
                for tool in tools:
                    if isinstance(tool, dict) and tool.get("enabled", False):
                        enabled_tools.append(tool.get("type", "Unknown"))
            logger.info(
                f"Enabled tools: {', '.join(enabled_tools) if enabled_tools else 'None'}"
            )
            return True
        else:
            logger.error(f"❌ {bot_name} not found: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Error getting {bot_name} info: {str(e)}")
        return False


def test_sql_query(bot_name, query, session_id, description):
    """Test a SQL query with the bot."""
    logger.info(f"\n--- Testing: {description} ---")
    logger.info(f"Query: {query}")

    payload = {"query": query, "session_id": session_id}

    try:
        response = requests.post(
            f"{API_BASE_URL}/bots/{bot_name}/query",
            json=payload,
            headers={"Content-Type": "application/json; charset=utf-8"},
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            logger.info(f"✅ Query successful")
            logger.info(f"Response: {result.get('response', 'No response')}")

            # Show tools used
            tools_used = result.get("tools_used", [])
            if tools_used:
                logger.info(f"Tools used: {', '.join(tools_used)}")
            else:
                logger.info("No tools were used")

            # Show tool outputs if available
            tool_outputs = result.get("tool_outputs", {})
            if tool_outputs:
                logger.info("Tool outputs:")
                for tool_name, output in tool_outputs.items():
                    if isinstance(output, dict):
                        success = output.get("success", False)
                        if success:
                            count = output.get("count", 0)
                            logger.info(f"  {tool_name}: ✅ Success, {count} results")
                        else:
                            error = output.get("error", "Unknown error")
                            logger.info(f"  {tool_name}: ❌ Error - {error}")
                    else:
                        logger.info(f"  {tool_name}: {str(output)[:100]}...")

            return True
        else:
            logger.error(f"❌ Query failed with status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Request failed: {str(e)}")
        return False


def test_connection_scenarios():
    """Test different database connection scenarios."""
    logger.info("=== Testing Database Connection Scenarios ===")

    # Test queries for different scenarios
    test_cases = [
        {
            "query": "Kaç tane personelimiz var?",
            "session_id": "test-remote-1",
            "description": "Turkish staff count query",
        },
        {
            "query": "List all departments with their locations",
            "session_id": "test-remote-2",
            "description": "English department listing query",
        },
        {
            "query": "Hangi bölümün en yüksek bütçesi var?",
            "session_id": "test-remote-3",
            "description": "Turkish budget query",
        },
        {
            "query": "Show me the department heads and their email addresses",
            "session_id": "test-remote-4",
            "description": "English staff details query",
        },
        {
            "query": "Bilgisayar Mühendisliği bölümünde kaç kişi çalışıyor?",
            "session_id": "test-remote-5",
            "description": "Turkish specific department query",
        },
    ]

    success_count = 0
    total_tests = len(test_cases)

    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"Test {i}/{total_tests}")

        success = test_sql_query(
            "AdminBot",
            test_case["query"],
            test_case["session_id"],
            test_case["description"],
        )

        if success:
            success_count += 1

        # Small delay between tests
        time.sleep(1)

    logger.info(f"\n{'='*60}")
    logger.info(f"Test Summary: {success_count}/{total_tests} successful")
    return success_count, total_tests


def check_current_config():
    """Check the current AdminBot configuration."""
    logger.info("=== Checking Current AdminBot Configuration ===")

    config_path = Path(__file__).parent.parent / "configs" / "admin_bot.yaml"

    if config_path.exists():
        with open(config_path, "r", encoding="utf-8") as f:
            content = f.read()

        logger.info("Current SQL configuration in admin_bot.yaml:")

        # Extract SQL tool configuration
        lines = content.split("\n")
        in_sql_config = False
        sql_lines = []

        for line in lines:
            if "type: SQLQueryTool" in line:
                in_sql_config = True
            elif in_sql_config and line.strip().startswith("- ") and "type:" in line:
                break
            elif in_sql_config:
                sql_lines.append(line)

        for line in sql_lines:
            if "connection_string" in line and not line.strip().startswith("#"):
                logger.info(f"  Active connection: {line.strip()}")
            elif "connection_string" in line:
                logger.info(f"  Commented: {line.strip()}")
    else:
        logger.error("❌ admin_bot.yaml not found")


def main():
    """Main test function."""
    logger.info("=== Remote SQL Database Live Testing ===")

    # Check if API is running
    if not test_api_connection():
        logger.error("Cannot proceed without API connection")
        sys.exit(1)

    # Check current configuration
    check_current_config()

    # Get AdminBot info
    if not get_bot_info("AdminBot"):
        logger.error("Cannot proceed without AdminBot")
        sys.exit(1)

    # Test different connection scenarios
    success_count, total_tests = test_connection_scenarios()

    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info("=== Final Summary ===")

    if success_count == total_tests:
        logger.info("🎉 All tests passed!")
        logger.info("✅ Remote SQL database functionality is working correctly")
    elif success_count > 0:
        logger.info(f"⚠️  Partial success: {success_count}/{total_tests} tests passed")
        logger.info("Some queries worked, check the logs above for details")
    else:
        logger.error("❌ All tests failed")
        logger.error("Check your database connection configuration")

    logger.info("\n📝 Notes:")
    logger.info("- Current configuration is shown above")
    logger.info("- To test with different databases, update configs/admin_bot.yaml")
    logger.info("- Make sure your database server is accessible")
    logger.info("- Check connection string format for your database type")


if __name__ == "__main__":
    main()
