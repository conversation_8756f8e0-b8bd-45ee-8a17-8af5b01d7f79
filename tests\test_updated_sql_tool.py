#!/usr/bin/env python3
"""
Test script for the updated SQL tool with remote database support.
This script tests both local SQLite and demonstrates remote connection configuration.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.tools.sql_query import SQLQueryTool

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configurations
SQLITE_CONFIG = {
    "connection_string": "sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite",
    "allowed_tables": ["staff", "departments", "budgets", "facilities"],
    "max_results": 50,
    "model": "gpt-4.1-mini",
    "temperature": 0.0
}

# Example remote PostgreSQL configuration (for demonstration)
POSTGRES_CONFIG = {
    "connection_string": "postgresql://postgres:admin123@localhost:5432/university_db",
    "allowed_tables": ["staff", "departments", "budgets", "facilities", "events"],
    "max_results": 50,
    "model": "gpt-4.1-mini",
    "temperature": 0.0
}

async def test_sqlite_connection():
    """Test SQLite database connection and queries."""
    logger.info("=== Testing SQLite Database Connection ===")
    
    sql_tool = SQLQueryTool(config=SQLITE_CONFIG)
    sql_tool.initialize()
    
    if not sql_tool.engine:
        logger.error("Failed to initialize SQLite engine")
        return False
    
    logger.info(f"✅ SQLite connection successful: {SQLITE_CONFIG['connection_string']}")
    
    # Test queries
    test_queries = [
        {
            "name": "List all staff",
            "query": "Tüm personeli listele",
            "description": "Turkish query to list all staff members"
        },
        {
            "name": "Department count",
            "query": "How many departments do we have?",
            "description": "English query for department count"
        },
        {
            "name": "Budget information",
            "query": "Bütçe bilgilerini göster",
            "description": "Turkish query for budget information"
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(test_queries, 1):
        logger.info(f"\n--- SQLite Test {i}: {test['name']} ---")
        logger.info(f"Query: {test['query']}")
        
        try:
            result = await sql_tool.execute(test['query'])
            
            if result.get('success', False):
                logger.info(f"✅ Success! Generated SQL: {result.get('query', 'N/A')}")
                logger.info(f"Results count: {result.get('count', 0)}")
                
                # Show first few results
                results = result.get('results', [])
                if results:
                    logger.info("Sample results:")
                    for j, row in enumerate(results[:2]):  # Show first 2 rows
                        logger.info(f"  Row {j+1}: {row}")
                    if len(results) > 2:
                        logger.info(f"  ... and {len(results) - 2} more rows")
                
                success_count += 1
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"❌ Exception occurred: {str(e)}")
    
    logger.info(f"\nSQLite tests: {success_count}/{len(test_queries)} successful")
    return success_count == len(test_queries)

async def test_direct_sql():
    """Test direct SQL execution."""
    logger.info("\n=== Testing Direct SQL Execution ===")
    
    sql_tool = SQLQueryTool(config=SQLITE_CONFIG)
    sql_tool.initialize()
    
    if not sql_tool.engine:
        logger.error("Failed to initialize SQL engine")
        return False
    
    # Direct SQL queries
    direct_queries = [
        {
            "name": "Count all tables",
            "sql": "SELECT name FROM sqlite_master WHERE type='table'",
            "description": "List all tables in SQLite database"
        },
        {
            "name": "Staff count",
            "sql": "SELECT COUNT(*) as total_staff FROM staff",
            "description": "Count total staff members"
        },
        {
            "name": "Department summary",
            "sql": "SELECT name, head, budget FROM departments ORDER BY budget DESC LIMIT 5",
            "description": "Top 5 departments by budget"
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(direct_queries, 1):
        logger.info(f"\n--- Direct SQL Test {i}: {test['name']} ---")
        logger.info(f"SQL: {test['sql']}")
        
        try:
            result = await sql_tool.execute("", sql_query=test['sql'], use_llm=False)
            
            if result.get('success', False):
                logger.info(f"✅ Success! Results count: {result.get('count', 0)}")
                
                results = result.get('results', [])
                if results:
                    logger.info("Results:")
                    for row in results:
                        logger.info(f"  {row}")
                
                success_count += 1
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"❌ Exception occurred: {str(e)}")
    
    logger.info(f"\nDirect SQL tests: {success_count}/{len(direct_queries)} successful")
    return success_count == len(direct_queries)

def demonstrate_remote_config():
    """Demonstrate how to configure remote database connections."""
    logger.info("\n=== Remote Database Configuration Examples ===")
    
    logger.info("To connect to a remote PostgreSQL database, update your bot config:")
    logger.info("```yaml")
    logger.info("tools:")
    logger.info("  - type: SQLQueryTool")
    logger.info("    enabled: true")
    logger.info("    config:")
    logger.info("      connection_string: postgresql://username:password@hostname:port/database_name")
    logger.info("      # Example: postgresql://admin:admin123@localhost:5432/university_db")
    logger.info("      allowed_tables: ['staff', 'departments', 'budgets']")
    logger.info("      max_results: 50")
    logger.info("```")
    
    logger.info("\nFor MySQL:")
    logger.info("connection_string: mysql+pymysql://username:password@hostname:port/database_name")
    
    logger.info("\nFor SQL Server:")
    logger.info("connection_string: mssql+pyodbc://username:password@hostname:port/database_name?driver=ODBC+Driver+17+for+SQL+Server")
    
    logger.info("\n📝 Note: Make sure to install the appropriate database drivers:")
    logger.info("- PostgreSQL: pip install psycopg2-binary")
    logger.info("- MySQL: pip install pymysql")
    logger.info("- SQL Server: pip install pyodbc")

def check_database_drivers():
    """Check if database drivers are installed."""
    logger.info("\n=== Checking Database Drivers ===")
    
    drivers = {
        "PostgreSQL": "psycopg2",
        "MySQL": "pymysql", 
        "SQL Server": "pyodbc"
    }
    
    for db_name, driver_name in drivers.items():
        try:
            __import__(driver_name)
            logger.info(f"✅ {db_name} driver ({driver_name}) is installed")
        except ImportError:
            logger.warning(f"❌ {db_name} driver ({driver_name}) is NOT installed")
            logger.info(f"   Install with: pip install {driver_name}")

async def main():
    """Main test function."""
    logger.info("=== SQL Tool Remote Database Support Test ===")
    
    # Check database drivers
    check_database_drivers()
    
    # Test SQLite connection
    sqlite_success = await test_sqlite_connection()
    
    # Test direct SQL execution
    direct_success = await test_direct_sql()
    
    # Show remote configuration examples
    demonstrate_remote_config()
    
    # Overall result
    if sqlite_success and direct_success:
        logger.info("\n🎉 All tests passed! SQL tool is working correctly.")
        logger.info("✅ Local SQLite database connection: Working")
        logger.info("✅ Natural language to SQL conversion: Working")
        logger.info("✅ Direct SQL execution: Working")
        logger.info("✅ Remote database drivers: Installed")
        logger.info("\nYou can now configure remote database connections in your bot YAML files.")
    else:
        logger.error("\n❌ Some tests failed. Please check the logs above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
