# Test script for AdminBot SQL functionality
# This script tests the AdminBot's SQL database connection and queries

# Set UTF-8 encoding for Turkish character support
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== AdminBot SQL Database Test ===" -ForegroundColor Green
Write-Host ""

# Test 1: Check if AdminBot is available
Write-Host "Test 1: Checking AdminBot availability..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Method GET -Uri "http://localhost:8000/bots/AdminBot" -ErrorAction Stop
    $botInfo = $response.Content | ConvertFrom-Json
    Write-Host "✅ AdminBot is available" -ForegroundColor Green
    Write-Host "Description: $($botInfo.description)" -ForegroundColor Cyan
    Write-Host ""
} catch {
    Write-Host "❌ AdminBot is not available: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Simple SQL query (Turkish)
Write-Host "Test 2: Testing Turkish SQL query..." -ForegroundColor Yellow
$body = @{
    query = "Kaç tane personelimiz var?"
    session_id = "test-admin-sql-1"
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Method POST -Uri "http://localhost:8000/bots/AdminBot/query" -Body $body -ContentType "application/json; charset=utf-8" -ErrorAction Stop
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ Turkish query successful" -ForegroundColor Green
    Write-Host "Response: $($result.response)" -ForegroundColor Cyan
    Write-Host "Tools used: $($result.tools_used -join ', ')" -ForegroundColor Magenta
    Write-Host ""
} catch {
    Write-Host "❌ Turkish query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

# Test 3: Department query (English)
Write-Host "Test 3: Testing English department query..." -ForegroundColor Yellow
$body = @{
    query = "List all departments with their locations"
    session_id = "test-admin-sql-2"
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Method POST -Uri "http://localhost:8000/bots/AdminBot/query" -Body $body -ContentType "application/json; charset=utf-8" -ErrorAction Stop
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ English department query successful" -ForegroundColor Green
    Write-Host "Response: $($result.response)" -ForegroundColor Cyan
    Write-Host "Tools used: $($result.tools_used -join ', ')" -ForegroundColor Magenta
    Write-Host ""
} catch {
    Write-Host "❌ English department query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

# Test 4: Budget query (Turkish)
Write-Host "Test 4: Testing Turkish budget query..." -ForegroundColor Yellow
$body = @{
    query = "Hangi bölümün en yüksek bütçesi var?"
    session_id = "test-admin-sql-3"
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Method POST -Uri "http://localhost:8000/bots/AdminBot/query" -Body $body -ContentType "application/json; charset=utf-8" -ErrorAction Stop
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ Turkish budget query successful" -ForegroundColor Green
    Write-Host "Response: $($result.response)" -ForegroundColor Cyan
    Write-Host "Tools used: $($result.tools_used -join ', ')" -ForegroundColor Magenta
    Write-Host ""
} catch {
    Write-Host "❌ Turkish budget query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

# Test 5: Staff query (English)
Write-Host "Test 5: Testing English staff query..." -ForegroundColor Yellow
$body = @{
    query = "Show me the department heads and their email addresses"
    session_id = "test-admin-sql-4"
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Method POST -Uri "http://localhost:8000/bots/AdminBot/query" -Body $body -ContentType "application/json; charset=utf-8" -ErrorAction Stop
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ English staff query successful" -ForegroundColor Green
    Write-Host "Response: $($result.response)" -ForegroundColor Cyan
    Write-Host "Tools used: $($result.tools_used -join ', ')" -ForegroundColor Magenta
    Write-Host ""
} catch {
    Write-Host "❌ English staff query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

# Test 6: Complex query (Turkish)
Write-Host "Test 6: Testing complex Turkish query..." -ForegroundColor Yellow
$body = @{
    query = "Bilgisayar Mühendisliği bölümünde kaç kişi çalışıyor ve bütçeleri ne kadar?"
    session_id = "test-admin-sql-5"
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Method POST -Uri "http://localhost:8000/bots/AdminBot/query" -Body $body -ContentType "application/json; charset=utf-8" -ErrorAction Stop
    $result = $response.Content | ConvertFrom-Json
    Write-Host "✅ Complex Turkish query successful" -ForegroundColor Green
    Write-Host "Response: $($result.response)" -ForegroundColor Cyan
    Write-Host "Tools used: $($result.tools_used -join ', ')" -ForegroundColor Magenta
    Write-Host ""
} catch {
    Write-Host "❌ Complex Turkish query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

Write-Host "=== Test Summary ===" -ForegroundColor Green
Write-Host "All AdminBot SQL tests completed!" -ForegroundColor Cyan
Write-Host ""
Write-Host "📝 Notes:" -ForegroundColor Yellow
Write-Host "- The AdminBot is using SQLite database: tests/test_db.sqlite" -ForegroundColor White
Write-Host "- To use a remote database, update the connection_string in configs/admin_bot.yaml" -ForegroundColor White
Write-Host "- Examples for remote databases are provided in the config file" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Remote Database Configuration Examples:" -ForegroundColor Yellow
Write-Host "PostgreSQL: postgresql://username:password@hostname:port/database_name" -ForegroundColor White
Write-Host "MySQL: mysql+pymysql://username:password@hostname:port/database_name" -ForegroundColor White
Write-Host "SQL Server: mssql+pyodbc://username:password@hostname:port/database_name?driver=ODBC+Driver+17+for+SQL+Server" -ForegroundColor White
