from sqlalchemy import create_engine, text
import pandas as pd

# Bağlantı bilgileri
server = "10.1.1.83"
database = "VERSISDB"
username = "ai-read"
password = "CHXG7jS2y9X4"
driver = "ODBC Driver 17 for SQL Server"

connection_string = (
    f"mssql+pyodbc://{username}:{password}@{server}/{database}"
    f"?driver={driver.replace(' ', '+')}&TrustServerCertificate=yes"
)

engine = create_engine(connection_string)

with engine.connect() as conn:
    # text() ile sorguyu sar
    tables = conn.execute(
        text(
            """
        SELECT TABLE_SCHEMA, TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
    """
        )
    ).fetchall()

all_dataframes = {}

for schema, table in tables:
    table_name = f"{schema}.{table}"
    query = f"SELECT TOP 5 * FROM [{schema}].[{table}]"
    try:
        df = pd.read_sql(query, engine)
        all_dataframes[table_name] = df
        print(f"✅ {table_name} yüklendi ({len(df)} satır).")
    except Exception as e:
        print(f"❌ {table_name} yüklenemedi: {e}")

print(all_dataframes.keys())
