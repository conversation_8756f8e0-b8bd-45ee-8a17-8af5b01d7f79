{"cells": [{"cell_type": "code", "execution_count": 4, "id": "76f73ef6", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'sqlalchemy'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01msqlalchemy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m create_engine, text\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# Bağlantı bilgileri\u001b[39;00m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'sqlalchemy'"]}], "source": ["from sqlalchemy import create_engine, text\n", "import pandas as pd\n", "\n", "# Bağlantı bilgileri\n", "server = \"10.1.1.83\"\n", "database = \"VERSISDB\"\n", "username = \"ai-read\"\n", "password = \"CHXG7jS2y9X4\"\n", "driver = \"ODBC Driver 17 for SQL Server\"\n", "\n", "connection_string = (\n", "    f\"mssql+pyodbc://{username}:{password}@{server}/{database}\"\n", "    f\"?driver={driver.replace(' ', '+')}&TrustServerCertificate=yes\"\n", ")\n", "\n", "engine = create_engine(connection_string)\n", "\n", "with engine.connect() as conn:\n", "    # text() ile sorguyu sar\n", "    tables = conn.execute(\n", "        text(\n", "            \"\"\"\n", "        SELECT TABLE_SCHEMA, TABLE_NAME \n", "        FROM INFORMATION_SCHEMA.TABLES \n", "        WHERE TABLE_TYPE = 'BASE TABLE'\n", "    \"\"\"\n", "        )\n", "    ).fetchall()\n", "\n", "all_dataframes = {}\n", "\n", "for schema, table in tables:\n", "    table_name = f\"{schema}.{table}\"\n", "    query = f\"SELECT TOP 5 * FROM [{schema}].[{table}]\"\n", "    try:\n", "        df = pd.read_sql(query, engine)\n", "        all_dataframes[table_name] = df\n", "        print(f\"✅ {table_name} yüklendi ({len(df)} satır).\")\n", "    except Exception as e:\n", "        print(f\"❌ {table_name} yüklenemedi: {e}\")\n", "\n", "print(all_dataframes.keys())\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}