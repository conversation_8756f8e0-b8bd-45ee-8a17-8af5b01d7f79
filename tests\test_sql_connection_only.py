#!/usr/bin/env python3
"""
Test script for SQL database connections without requiring OpenAI API key.
This script tests direct SQL execution to verify database connectivity.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.tools.sql_query import SQLQueryTool

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configurations
SQLITE_CONFIG = {
    "connection_string": "sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite",
    "allowed_tables": ["staff", "departments", "budgets", "facilities"],
    "max_results": 50,
    "model": "gpt-4.1-mini",
    "temperature": 0.0,
}


async def test_sqlite_direct_sql():
    """Test direct SQL execution without LLM."""
    logger.info("=== Testing SQLite Database with Direct SQL ===")

    sql_tool = SQLQueryTool(config=SQLITE_CONFIG)
    sql_tool.initialize()

    if not sql_tool.engine:
        logger.error("Failed to initialize SQL engine")
        return False

    logger.info(
        f"✅ SQLite connection successful: {SQLITE_CONFIG['connection_string']}"
    )

    # Direct SQL queries that don't require LLM
    direct_queries = [
        {
            "name": "List all tables",
            "sql": "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name",
            "description": "List all tables in the database",
        },
        {
            "name": "Staff count",
            "sql": "SELECT COUNT(*) as total_staff FROM staff",
            "description": "Count total staff members",
        },
        {
            "name": "Department list",
            "sql": "SELECT id, name, location FROM departments ORDER BY name",
            "description": "List all departments",
        },
        {
            "name": "Staff by department",
            "sql": "SELECT d.name as department, COUNT(*) as count FROM staff s JOIN departments d ON s.department_id = d.id GROUP BY d.name ORDER BY count DESC",
            "description": "Staff count by department",
        },
        {
            "name": "Sample staff data",
            "sql": "SELECT s.name, d.name as department, s.position, s.email FROM staff s JOIN departments d ON s.department_id = d.id LIMIT 5",
            "description": "Sample staff records",
        },
        {
            "name": "Budget summary",
            "sql": "SELECT d.name as department, b.fiscal_year, b.amount, b.status FROM budgets b JOIN departments d ON b.department_id = d.id ORDER BY b.fiscal_year DESC, b.amount DESC",
            "description": "Budget information",
        },
    ]

    success_count = 0

    for i, test in enumerate(direct_queries, 1):
        logger.info(f"\n--- Test {i}: {test['name']} ---")
        logger.info(f"Description: {test['description']}")
        logger.info(f"SQL: {test['sql']}")

        try:
            # Execute direct SQL without LLM
            result = await sql_tool.execute("", sql_query=test["sql"], use_llm=False)

            if result.get("success", False):
                logger.info(f"✅ Success! Results count: {result.get('count', 0)}")

                results = result.get("results", [])
                if results:
                    logger.info("Results:")
                    for j, row in enumerate(results):
                        if j < 5:  # Show first 5 rows
                            logger.info(f"  {row}")
                        elif j == 5:
                            logger.info(f"  ... and {len(results) - 5} more rows")
                            break
                else:
                    logger.info("No results returned")

                success_count += 1
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            logger.error(f"❌ Exception occurred: {str(e)}")

        logger.info("-" * 50)

    logger.info(f"\nDirect SQL tests: {success_count}/{len(direct_queries)} successful")
    return success_count == len(direct_queries)


def test_remote_connection_strings():
    """Test different connection string formats (without actually connecting)."""
    logger.info("\n=== Testing Connection String Parsing ===")

    connection_strings = [
        {
            "type": "SQLite",
            "connection_string": "sqlite:///./test.db",
            "description": "Local SQLite database",
        },
        {
            "type": "PostgreSQL",
            "connection_string": "postgresql://user:pass@localhost:5432/dbname",
            "description": "PostgreSQL database",
        },
        {
            "type": "MySQL",
            "connection_string": "mysql+pymysql://user:pass@localhost:3306/dbname",
            "description": "MySQL database",
        },
        {
            "type": "SQL Server",
            "connection_string": "mssql+pyodbc://user:pass@localhost:1433/dbname?driver=ODBC+Driver+17+for+SQL+Server",
            "description": "SQL Server database",
        },
    ]

    for conn_info in connection_strings:
        logger.info(f"\n{conn_info['type']}:")
        logger.info(f"  Connection String: {conn_info['connection_string']}")
        logger.info(f"  Description: {conn_info['description']}")

        # Test if the connection string can be parsed by SQLAlchemy
        try:
            from sqlalchemy import create_engine

            engine = create_engine(
                conn_info["connection_string"],
                strategy="mock",
                executor=lambda sql, *_: None,
            )
            logger.info(f"  ✅ Connection string format is valid")
        except Exception as e:
            logger.error(f"  ❌ Invalid connection string format: {str(e)}")


def show_configuration_examples():
    """Show configuration examples for different databases."""
    logger.info("\n=== Configuration Examples ===")

    logger.info("\n1. SQLite (Local Database):")
    logger.info("```yaml")
    logger.info("tools:")
    logger.info("  - type: SQLQueryTool")
    logger.info("    enabled: true")
    logger.info("    config:")
    logger.info("      connection_string: sqlite:///./tests/test_db.sqlite")
    logger.info("      allowed_tables: ['staff', 'departments', 'budgets']")
    logger.info("      max_results: 50")
    logger.info("```")

    logger.info("\n2. PostgreSQL (Remote Database):")
    logger.info("```yaml")
    logger.info("tools:")
    logger.info("  - type: SQLQueryTool")
    logger.info("    enabled: true")
    logger.info("    config:")
    logger.info(
        "      connection_string: postgresql://username:password@hostname:port/database_name"
    )
    logger.info(
        "      # Example: postgresql://admin:<EMAIL>:5432/university_db"
    )
    logger.info(
        "      allowed_tables: ['staff', 'departments', 'budgets', 'facilities']"
    )
    logger.info("      max_results: 50")
    logger.info("```")

    logger.info("\n3. MySQL (Remote Database):")
    logger.info("```yaml")
    logger.info("tools:")
    logger.info("  - type: SQLQueryTool")
    logger.info("    enabled: true")
    logger.info("    config:")
    logger.info(
        "      connection_string: mysql+pymysql://username:password@hostname:port/database_name"
    )
    logger.info(
        "      # Example: mysql+pymysql://admin:<EMAIL>:3306/university_db"
    )
    logger.info(
        "      allowed_tables: ['staff', 'departments', 'budgets', 'facilities']"
    )
    logger.info("      max_results: 50")
    logger.info("```")

    logger.info("\n4. SQL Server (Remote Database):")
    logger.info("```yaml")
    logger.info("tools:")
    logger.info("  - type: SQLQueryTool")
    logger.info("    enabled: true")
    logger.info("    config:")
    logger.info(
        "      connection_string: mssql+pyodbc://username:password@hostname:port/database_name?driver=ODBC+Driver+17+for+SQL+Server"
    )
    logger.info(
        "      # Example: mssql+pyodbc://admin:<EMAIL>:1433/university_db?driver=ODBC+Driver+17+for+SQL+Server"
    )
    logger.info(
        "      allowed_tables: ['staff', 'departments', 'budgets', 'facilities']"
    )
    logger.info("      max_results: 50")
    logger.info("```")


async def main():
    """Main test function."""
    logger.info("=== SQL Database Connection Test (No API Key Required) ===")

    # Test SQLite direct SQL execution
    sqlite_success = await test_sqlite_direct_sql()

    # Test connection string parsing
    test_remote_connection_strings()

    # Show configuration examples
    show_configuration_examples()

    # Overall result
    if sqlite_success:
        logger.info("\n🎉 Database connection test successful!")
        logger.info("✅ SQLite database connection: Working")
        logger.info("✅ Direct SQL execution: Working")
        logger.info("✅ Database drivers installed: PostgreSQL, MySQL, SQL Server")
        logger.info("\n📝 Next Steps:")
        logger.info(
            "1. Update your bot YAML configuration with the appropriate connection string"
        )
        logger.info("2. Set up your remote database server")
        logger.info(
            "3. Test with OpenAI API key for natural language to SQL conversion"
        )
        logger.info(
            "\n💡 For natural language queries, you'll need to set OPENAI_API_KEY environment variable"
        )
    else:
        logger.error(
            "\n❌ Database connection test failed. Please check the logs above."
        )
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
