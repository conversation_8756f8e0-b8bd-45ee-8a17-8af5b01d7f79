# Chapter 2: Bot

Welcome back! In Chapter 1, we introduced the big picture: the [AgenticRAG System](01_agenticrag_system_.md), which acts like the "central command center" or "orchestra conductor" for our question-answering system. It manages everything but doesn't necessarily answer questions itself.

So, if the [AgenticRAG System](01_agenticrag_system_.md) is the manager, who does the actual specialized work? That's where the **Bot** comes in!

## What Problem Does the Bot Solve?

Imagine you have different kinds of questions. Some might be about internal company documents, others might require searching the web, and some might need querying a specific database.

Trying to build a single, giant AI that can handle *all* these different types of questions perfectly can be really hard. It would need to know about documents, databases, the internet, *and* figure out which to use for every single question.

This is where the **Bot** concept is super helpful! Instead of one big AI, we create **specialized AI assistants**, each focused on a particular area or task. We call each of these specialists a "Bot".

Think of a **Bot** as a specialized employee trained for a specific job.

*   You might have an "AcademicBot" for questions about research papers.
*   A "StudentBot" for questions about university life.
*   An "AdminBot" for questions about internal operations.

Each of these Bots can be fine-tuned and given specific capabilities relevant to its job. The [AgenticRAG System](01_agenticrag_system_.md) knows *which* Bot to send a query to based on how you interact with the system (remember the `/bots/{bot_name}/query` endpoint?).

## What Makes Up a Bot?

A Bot isn't just a name; it's a collection of specific tools and logic tailored for its purpose. Based on the concept details, a Bot is defined by:

1.  **Its Purpose/Domain:** This is its specialization (e.g., "Academic", "Student", "Admin"). It helps you understand what kind of questions this Bot is good at answering.
2.  **Its Tools:** This is crucial! Each Bot is equipped with a specific set of [Tool](04_tool_.md)s it's allowed to use. An AcademicBot might have a "DocumentSearchTool" for papers and a "DatabaseTool" for researcher info, while a SimpleBot might only have a "WebSearchTool". We'll dive into [Tool](04_tool_.md)s in Chapter 4.
3.  **Its Prompts:** These are the instructions given to the AI brain to define its style, personality, and baseline knowledge. For example, a StudentBot might have prompts that make it friendly and helpful, while an AdminBot's prompts might make it more formal and direct.
4.  **Its Underlying AI Agent Logic:** This is the actual "brain" of the Bot that takes the user's question, uses the available [Tool](04_tool_.md)s if needed, considers the prompts, and generates a response. In this project, the specific type of agent used is the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md), which we'll explore in Chapter 5.

So, when you interact with a `StudentBot`, you are specifically talking to the AI assistant configured *with* the StudentBot's particular set of tools, prompts, and agent settings.

## How Do You Interact With a Specific Bot?

As we saw in Chapter 1, the main way to interact with the system from the outside is through the `/bots/{bot_name}/query` endpoint provided by the FastAPI application.

```python
# Simplified snippet from app\main.py
@app.post("/bots/{bot_name}/query", tags=["Queries"])
async def query_bot(
    bot_name: str, request: QueryRequest, rag: AgenticRAG = Depends(get_agentic_rag)
):
    """Query a specific bot."""
    try:
        # Get the AgenticRAG instance (the command center)
        # ... get the specific bot ...

        # Ask the AgenticRAG instance to process the query
        response = await rag.process_query(bot_name, request)

        # ... format and return the response ...
```

This snippet shows that when you want to ask a question, you **specify which bot you want to talk to** using the `bot_name` in the URL path (like `/bots/StudentBot/query`). The `request` object contains your actual question.

The [AgenticRAG System](01_agenticrag_system_.md) then takes this request and directs it specifically to the components (tools, router, agent) that belong to the `StudentBot`.

## How Does the System Know About Bots? (Under the Hood)

The [AgenticRAG System](01_agenticrag_system_.md) needs to know *about* each bot and load its configuration when the system starts up. This is done by reading configuration files.

Each Bot is defined by a separate configuration file, typically in YAML format, located in the `configs` directory. For example, there might be `configs/academic_bot.yaml`, `configs/student_bot.yaml`, etc.

Let's look at a super simplified version of what a bot configuration file might look like (`configs/simple_bot.yaml`):

```yaml
# Simplified snippet from configs/simple_bot.yaml
name: SimpleBot
description: A basic AI assistant.

prompts:
  system_prompt_path: simple_bot/system.txt # Points to the system instructions
  query_prompt_path: simple_bot/query.txt   # Points to the query handling instructions

tools:
  - type: WebSearchTool # This bot has a web search tool
    enabled: true
    config:
      max_results: 5

agent:
  type: langgraph       # Specifies the type of agent logic
  model: gpt-4o-mini    # Specifies the AI model to use
  config:
    temperature: 0.2
```

**Explanation:**

*   `name`: This gives the bot its unique identifier (`SimpleBot`).
*   `description`: A human-readable explanation of what the bot does.
*   `prompts`: Specifies the files containing the text instructions (`system.txt`, `query.txt`) that guide the AI's behavior and knowledge.
*   `tools`: This is a list of the [Tool](04_tool_.md)s this specific bot is allowed to use. Here, the SimpleBot only has a `WebSearchTool`.
*   `agent`: Configures the specific [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) that serves as the Bot's brain, including which AI model to use.

When the [AgenticRAG System](01_agenticrag_system_.md) starts, its `_load_bots` method scans the `configs` directory, reads each of these YAML files, and uses the information to set up each Bot internally.

```python
# Simplified snippet from app\core\agentic_rag.py
class AgenticRAG:
    # ... other parts ...

    def _load_bots(self) -> None:
        """Load all bot configurations and initialize components."""
        bot_configs = self.config_loader.get_all_configs() # Reads all YAML files

        for bot_name, bot_config in bot_configs.items(): # Loop through each config file
            try:
                # Initialize tools specific to this bot config
                tools = self._initialize_tools(bot_config)

                # Load prompts specific to this bot config
                # ... load prompts using paths from bot_config.prompts ...

                # Initialize the query router for THIS bot
                query_router = QueryRouter(bot_config, tools)

                # Initialize the agent for THIS bot
                agent = LangGraphAgent(bot_config.agent, system_prompt, query_prompt)

                # Store all HIS bot's parts under its name
                self.bots[bot_name] = {
                    "config": bot_config,
                    "tools": tools,
                    "query_router": query_router,
                    "agent": agent,
                }
                logger.info(f"Loaded bot: {bot_name}")
            except Exception as e:
                logger.error(f"Error loading bot {bot_name}: {str(e)}")

```

**Explanation:**

*   The `_load_bots` method loops through each configuration found (like `academic_bot.yaml`, `student_bot.yaml`, etc.).
*   Inside the loop, for `SimpleBot`, it reads its configuration (`bot_config`).
*   It then uses that *specific* `bot_config` to set up the `SimpleBot`'s `tools`, `query_router`, and `agent`.
*   Finally, it stores these components together in the `self.bots` dictionary, using `SimpleBot` as the key.

This is how the [AgenticRAG System](01_agenticrag_system_.md) keeps track of multiple specialized Bots, each with its own independent setup.

## What Happens When You Query a Bot?

When you send a query to, say, `/bots/AcademicBot/query`, here's a simplified view of the flow (based on the diagram from Chapter 1, but now focusing on the specific Bot's components):

```mermaid
sequenceDiagram
    participant User
    participant FastAPIA as FastAPI App
    participant AgenticRAG as AgenticRAG System
    participant AcademicBotComponents as Academic Bot's Parts (Router, Agent, Tools)
    participant QueryRouter as Query Router (AcademicBot's)
    participant Agent as Agent (AcademicBot's)
    participant Tool as Tool (AcademicBot's)

    User->>FastAPIA: Query Request (/bots/AcademicBot/query)
    FastAPIA->>AgenticRAG: process_query("AcademicBot", request)
    AgenticRAG->>AgenticRAG: Find "AcademicBot" in self.bots
    AgenticRAG->>QueryRouter: route_query(user_query, ...)
    QueryRouter->>Tool: Use Academic Bot's Tool (if needed)
    Tool-->>QueryRouter: Tool Results
    QueryRouter-->>AgenticRAG: Tool results
    AgenticRAG->>Agent: process_query(user_query, tool_results, ...)
    Agent-->>AgenticRAG: Final response text
    AgenticRAG->>FastAPIA: QueryResponse (formatted)
    FastAPIA->>User: JSON Response
```

**Explanation:**

1.  The **User** queries the **FastAPI App** specifying `AcademicBot`.
2.  The **FastAPI App** calls `process_query` on the **AgenticRAG System**, passing `"AcademicBot"` and the query.
3.  The **AgenticRAG System** looks up `AcademicBot` in its `self.bots` dictionary.
4.  It retrieves the specific [Query Router](03_query_router.md) instance, the specific [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) instance, and the list of [Tool](04_tool.md)s loaded for the `AcademicBot`.
5.  It passes the query to the `AcademicBot`'s specific [Query Router](03_query_router.md).
6.  The `AcademicBot`'s [Query Router](03_query_router.md) might decide to use one of the `AcademicBot`'s enabled [Tool](04_tool.md)s (like the DocumentSearchTool).
7.  Tool results (if any) are returned to the `AcademicBot`'s [Query Router](03_query_router.md), which passes them back to the **AgenticRAG System**.
8.  The **AgenticRAG System** then calls the `process_query` method on the `AcademicBot`'s specific [Agent (LangGraphAgent)](05_agent__langgraphagent__.md), providing the original query *and* the tool results.
9.  The `AcademicBot`'s [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) generates the final answer using the information.
10. The **AgenticRAG System** formats the final response and sends it back.

This flow highlights that querying a specific Bot means you are engaging *only* that Bot's defined capabilities and logic. The other Bots remain separate and untouched.

## Conclusion

In this chapter, we learned that **Bots** are the specialized units within the [AgenticRAG System](01_agenticrag_system_.md). Each Bot is configured for a particular domain or purpose, possessing its own set of allowed [Tool](04_tool_.md)s, specific prompts that define its style, and its own underlying AI agent logic ([Agent (LangGraphAgent)](05_agent__langgraphagent__.md)). They are defined via configuration files and loaded by the [AgenticRAG System](01_agenticrag_system_.md) at startup. When you query a specific Bot, the [AgenticRAG System](01_agenticrag_system_.md) directs your request to *only* that Bot's unique components.

Now that we understand the role of a Bot as a specialized entity, let's look closer at one of the key components * inside* a Bot: the **Query Router**.

[Query Router](03_query_router_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)