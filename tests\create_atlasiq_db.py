#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a test SQLite database for AtlasIQBot.

This script creates a sample academic database with tables for:
- researchers
- institutions
- grants
- publications

These tables match the allowed_tables in the atlasiq_bot.yaml configuration.
"""

import os
import sys
import sqlite3
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Database file path
DB_PATH = "atlasiq_test_db.sqlite"


def create_atlasiq_database():
    """Create the AtlasIQ test database with academic tables and data."""
    # Check if the database already exists
    if os.path.exists(DB_PATH):
        logger.info(f"Database {DB_PATH} already exists. Removing it...")
        os.remove(DB_PATH)

    # Create a new database
    logger.info(f"Creating AtlasIQ database {DB_PATH}...")
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create tables
    logger.info("Creating academic tables...")

    # Institutions table
    cursor.execute(
        """
    CREATE TABLE institutions (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        city TEXT,
        country TEXT,
        established_year INTEGER,
        website TEXT,
        contact_email TEXT
    )
    """
    )

    # Researchers table
    cursor.execute(
        """
    CREATE TABLE researchers (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        title TEXT,
        institution_id INTEGER,
        department TEXT,
        research_areas TEXT,
        email TEXT,
        orcid TEXT,
        h_index INTEGER,
        FOREIGN KEY (institution_id) REFERENCES institutions (id)
    )
    """
    )

    # Grants table
    cursor.execute(
        """
    CREATE TABLE grants (
        id INTEGER PRIMARY KEY,
        title TEXT NOT NULL,
        funding_agency TEXT,
        principal_investigator_id INTEGER,
        institution_id INTEGER,
        amount REAL,
        currency TEXT,
        start_date TEXT,
        end_date TEXT,
        status TEXT,
        research_area TEXT,
        FOREIGN KEY (principal_investigator_id) REFERENCES researchers (id),
        FOREIGN KEY (institution_id) REFERENCES institutions (id)
    )
    """
    )

    # Publications table
    cursor.execute(
        """
    CREATE TABLE publications (
        id INTEGER PRIMARY KEY,
        title TEXT NOT NULL,
        authors TEXT,
        journal TEXT,
        publication_year INTEGER,
        volume TEXT,
        issue TEXT,
        pages TEXT,
        doi TEXT,
        citation_count INTEGER,
        research_area TEXT,
        institution_id INTEGER,
        grant_id INTEGER,
        FOREIGN KEY (institution_id) REFERENCES institutions (id),
        FOREIGN KEY (grant_id) REFERENCES grants (id)
    )
    """
    )

    # Insert sample data
    logger.info("Inserting sample academic data...")

    # Insert institutions (Turkish universities and research centers)
    institutions = [
        (
            1,
            "Atlas Üniversitesi",
            "Private University",
            "İstanbul",
            "Türkiye",
            2018,
            "https://atlas.edu.tr",
            "<EMAIL>",
        ),
        (
            2,
            "İstanbul Teknik Üniversitesi",
            "Public University",
            "İstanbul",
            "Türkiye",
            1773,
            "https://itu.edu.tr",
            "<EMAIL>",
        ),
        (
            3,
            "Boğaziçi Üniversitesi",
            "Public University",
            "İstanbul",
            "Türkiye",
            1863,
            "https://boun.edu.tr",
            "<EMAIL>",
        ),
        (
            4,
            "Orta Doğu Teknik Üniversitesi",
            "Public University",
            "Ankara",
            "Türkiye",
            1956,
            "https://metu.edu.tr",
            "<EMAIL>",
        ),
        (
            5,
            "Sabancı Üniversitesi",
            "Private University",
            "İstanbul",
            "Türkiye",
            1994,
            "https://sabanciuniv.edu",
            "<EMAIL>",
        ),
        (
            6,
            "TÜBİTAK",
            "Research Institution",
            "Ankara",
            "Türkiye",
            1963,
            "https://tubitak.gov.tr",
            "<EMAIL>",
        ),
        (
            7,
            "Koç Üniversitesi",
            "Private University",
            "İstanbul",
            "Türkiye",
            1993,
            "https://ku.edu.tr",
            "<EMAIL>",
        ),
        (
            8,
            "Bilkent Üniversitesi",
            "Private University",
            "Ankara",
            "Türkiye",
            1984,
            "https://bilkent.edu.tr",
            "<EMAIL>",
        ),
    ]
    cursor.executemany(
        "INSERT INTO institutions VALUES (?, ?, ?, ?, ?, ?, ?, ?)", institutions
    )

    # Insert researchers
    researchers = [
        (
            1,
            "Prof. Dr. Mehmet Özkan",
            "Professor",
            1,
            "Computer Science",
            "Artificial Intelligence, Machine Learning",
            "<EMAIL>",
            "0000-0001-2345-6789",
            25,
        ),
        (
            2,
            "Doç. Dr. Ayşe Yılmaz",
            "Associate Professor",
            1,
            "Computer Science",
            "Natural Language Processing, Deep Learning",
            "<EMAIL>",
            "0000-0002-3456-7890",
            18,
        ),
        (
            3,
            "Prof. Dr. Ahmet Kaya",
            "Professor",
            2,
            "Computer Engineering",
            "Computer Vision, Robotics",
            "<EMAIL>",
            "0000-0003-4567-8901",
            35,
        ),
        (
            4,
            "Prof. Dr. Zeynep Demir",
            "Professor",
            3,
            "Computer Engineering",
            "Data Mining, Big Data Analytics",
            "<EMAIL>",
            "0000-0004-5678-9012",
            42,
        ),
        (
            5,
            "Dr. Öğr. Üyesi Emre Şahin",
            "Assistant Professor",
            4,
            "Computer Engineering",
            "Cybersecurity, Network Security",
            "<EMAIL>",
            "0000-0005-6789-0123",
            12,
        ),
        (
            6,
            "Prof. Dr. Selin Arslan",
            "Professor",
            5,
            "Computer Science",
            "Human-Computer Interaction, UX Design",
            "<EMAIL>",
            "0000-0006-7890-1234",
            28,
        ),
        (
            7,
            "Doç. Dr. Burak Yıldız",
            "Associate Professor",
            6,
            "Information Technologies",
            "Quantum Computing, Algorithms",
            "<EMAIL>",
            "0000-0007-8901-2345",
            22,
        ),
        (
            8,
            "Prof. Dr. Deniz Çelik",
            "Professor",
            7,
            "Computer Engineering",
            "Software Engineering, Agile Methods",
            "<EMAIL>",
            "0000-0008-9012-3456",
            31,
        ),
        (
            9,
            "Dr. Öğr. Üyesi Murat Kara",
            "Assistant Professor",
            8,
            "Computer Science",
            "Blockchain, Distributed Systems",
            "<EMAIL>",
            "0000-0009-0123-4567",
            15,
        ),
        (
            10,
            "Arş. Gör. Dr. Elif Öztürk",
            "Research Assistant",
            1,
            "Computer Science",
            "Computer Graphics, Virtual Reality",
            "<EMAIL>",
            "0000-0010-1234-5678",
            8,
        ),
    ]
    cursor.executemany(
        "INSERT INTO researchers VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", researchers
    )

    # Insert grants
    grants = [
        (
            1,
            "Yapay Zeka Tabanlı Eğitim Sistemleri",
            "TÜBİTAK",
            1,
            1,
            250000.00,
            "TL",
            "2023-01-01",
            "2025-12-31",
            "Active",
            "Artificial Intelligence",
        ),
        (
            2,
            "Doğal Dil İşleme ile Türkçe Metin Analizi",
            "TÜBİTAK",
            2,
            1,
            180000.00,
            "TL",
            "2022-06-01",
            "2024-05-31",
            "Active",
            "Natural Language Processing",
        ),
        (
            3,
            "Otonom Araçlar için Bilgisayarlı Görü",
            "TÜBA",
            3,
            2,
            320000.00,
            "TL",
            "2023-03-01",
            "2026-02-28",
            "Active",
            "Computer Vision",
        ),
        (
            4,
            "Büyük Veri Analitiği ve Makine Öğrenmesi",
            "Avrupa Birliği Horizon",
            4,
            3,
            450000.00,
            "EUR",
            "2022-09-01",
            "2025-08-31",
            "Active",
            "Data Science",
        ),
        (
            5,
            "Siber Güvenlik ve Kriptografi",
            "NATO SPS",
            5,
            4,
            200000.00,
            "USD",
            "2023-07-01",
            "2025-06-30",
            "Active",
            "Cybersecurity",
        ),
        (
            6,
            "İnsan-Bilgisayar Etkileşimi Araştırmaları",
            "TÜBİTAK",
            6,
            5,
            150000.00,
            "TL",
            "2022-01-01",
            "2024-12-31",
            "Active",
            "HCI",
        ),
        (
            7,
            "Kuantum Hesaplama Algoritmaları",
            "TÜBİTAK",
            7,
            6,
            300000.00,
            "TL",
            "2023-05-01",
            "2026-04-30",
            "Active",
            "Quantum Computing",
        ),
        (
            8,
            "Yazılım Mühendisliği ve DevOps",
            "Sanayi Bakanlığı",
            8,
            7,
            120000.00,
            "TL",
            "2022-10-01",
            "2024-09-30",
            "Active",
            "Software Engineering",
        ),
        (
            9,
            "Blockchain ve Dağıtık Sistemler",
            "TÜBİTAK",
            9,
            8,
            220000.00,
            "TL",
            "2023-02-01",
            "2025-01-31",
            "Active",
            "Blockchain",
        ),
        (
            10,
            "Sanal Gerçeklik ve Artırılmış Gerçeklik",
            "Atlas Üniversitesi",
            10,
            1,
            80000.00,
            "TL",
            "2023-09-01",
            "2024-08-31",
            "Active",
            "Virtual Reality",
        ),
    ]
    cursor.executemany(
        "INSERT INTO grants VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", grants
    )

    # Insert publications
    publications = [
        (
            1,
            "Deep Learning Approaches for Turkish Language Processing",
            "Mehmet Özkan, Ayşe Yılmaz",
            "Journal of Artificial Intelligence Research",
            2023,
            "45",
            "3",
            "123-145",
            "10.1613/jair.1.12345",
            15,
            "Natural Language Processing",
            1,
            1,
        ),
        (
            2,
            "Computer Vision Techniques for Autonomous Vehicle Navigation",
            "Ahmet Kaya, Zeynep Demir",
            "IEEE Transactions on Intelligent Transportation Systems",
            2023,
            "24",
            "8",
            "4567-4580",
            "10.1109/TITS.2023.12345",
            28,
            "Computer Vision",
            2,
            3,
        ),
        (
            3,
            "Big Data Analytics in Healthcare: A Turkish Perspective",
            "Zeynep Demir, Selin Arslan",
            "Nature Digital Medicine",
            2022,
            "5",
            "1",
            "78-92",
            "10.1038/s41746-022-12345",
            42,
            "Data Science",
            3,
            4,
        ),
        (
            4,
            "Cybersecurity Threats in IoT Networks",
            "Emre Şahin, Burak Yıldız",
            "Computers & Security",
            2023,
            "128",
            "",
            "103156",
            "10.1016/j.cose.2023.103156",
            18,
            "Cybersecurity",
            4,
            5,
        ),
        (
            5,
            "Human-Computer Interaction in Educational Technologies",
            "Selin Arslan, Elif Öztürk",
            "International Journal of Human-Computer Studies",
            2022,
            "167",
            "",
            "102891",
            "10.1016/j.ijhcs.2022.102891",
            22,
            "Human-Computer Interaction",
            5,
            6,
        ),
        (
            6,
            "Quantum Algorithms for Optimization Problems",
            "Burak Yıldız, Murat Kara",
            "Quantum Information Processing",
            2023,
            "22",
            "4",
            "156",
            "10.1007/s11128-023-03912-x",
            12,
            "Quantum Computing",
            6,
            7,
        ),
        (
            7,
            "Agile Software Development in Turkish IT Industry",
            "Deniz Çelik, Mehmet Özkan",
            "Information and Software Technology",
            2023,
            "158",
            "",
            "107182",
            "10.1016/j.infsof.2023.107182",
            8,
            "Software Engineering",
            7,
            8,
        ),
        (
            8,
            "Blockchain Applications in Supply Chain Management",
            "Murat Kara, Ahmet Kaya",
            "Computers & Industrial Engineering",
            2022,
            "172",
            "",
            "108579",
            "10.1016/j.cie.2022.108579",
            25,
            "Blockchain",
            8,
            9,
        ),
        (
            9,
            "Virtual Reality in Medical Education: Turkish Case Study",
            "Elif Öztürk, Ayşe Yılmaz",
            "Computers & Education",
            2023,
            "195",
            "",
            "104712",
            "10.1016/j.compedu.2022.104712",
            19,
            "Virtual Reality",
            1,
            10,
        ),
        (
            10,
            "Machine Learning for Turkish Text Classification",
            "Ayşe Yılmaz, Mehmet Özkan",
            "Expert Systems with Applications",
            2022,
            "203",
            "",
            "117456",
            "10.1016/j.eswa.2022.117456",
            31,
            "Machine Learning",
            1,
            2,
        ),
    ]
    cursor.executemany(
        "INSERT INTO publications VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        publications,
    )

    # Commit changes and close connection
    conn.commit()
    conn.close()

    logger.info(f"AtlasIQ database {DB_PATH} created successfully!")
    return os.path.abspath(DB_PATH)


if __name__ == "__main__":
    db_path = create_atlasiq_database()
    print(f"AtlasIQ test database created at: {db_path}")
