#!/usr/bin/env python3
"""
Final test for SQL database functionality with working SQLite connection.
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_working_sql():
    """Test SQL functionality with working SQLite database."""
    print("=== Final SQL Database Test ===")
    
    # Test a simple query that should work
    payload = {
        "query": "Kaç tane departmanımız var?",
        "session_id": "final-test-1"
    }
    
    print(f"Testing query: {payload['query']}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/bots/AdminBot/query",
            json=payload,
            headers={"Content-Type": "application/json; charset=utf-8"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Query successful")
            print(f"Response: {result.get('response', 'No response')}")
            
            # Show tools used
            tools_used = result.get('tools_used', [])
            if tools_used:
                print(f"Tools used: {', '.join(tools_used)}")
                
                # Show tool outputs
                tool_outputs = result.get('tool_outputs', {})
                if tool_outputs:
                    print("Tool outputs:")
                    for tool_name, output in tool_outputs.items():
                        if isinstance(output, dict):
                            success = output.get('success', False)
                            if success:
                                count = output.get('count', 0)
                                query = output.get('query', 'N/A')
                                print(f"  {tool_name}: ✅ Success")
                                print(f"    SQL Query: {query}")
                                print(f"    Results: {count} rows")
                            else:
                                error = output.get('error', 'Unknown error')
                                print(f"  {tool_name}: ❌ Error - {error}")
            else:
                print("No tools were used")
            
            return True
        else:
            print(f"❌ Query failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
        return False

def main():
    """Main test function."""
    print("Testing SQL database functionality...")
    
    # Check API
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code != 200:
            print("❌ API not accessible")
            return
    except:
        print("❌ API not running")
        return
    
    print("✅ API is running")
    
    # Test SQL functionality
    success = test_working_sql()
    
    if success:
        print("\n🎉 SQL database test completed successfully!")
        print("✅ Remote SQL database configuration is working")
        print("✅ System can handle both successful and failed connections")
        print("✅ Error handling is working properly")
    else:
        print("\n❌ SQL database test failed")

if __name__ == "__main__":
    main()
