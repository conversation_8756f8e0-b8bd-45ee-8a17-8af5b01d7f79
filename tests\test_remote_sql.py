#!/usr/bin/env python3
"""
Test script for remote SQL database connection.
This script tests the AdminBot's SQL functionality with a remote PostgreSQL database.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.tools.sql_query import SQLQueryTool

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration for PostgreSQL
TEST_CONFIG = {
    "connection_string": "postgresql://postgres:admin123@localhost:5432/university_db",
    "allowed_tables": ["staff", "departments", "budgets", "facilities", "events"],
    "max_results": 50,
    "model": "gpt-4.1-mini",
    "temperature": 0.0
}

async def test_sql_tool():
    """Test the SQL tool with various queries."""
    logger.info("Testing SQL tool with remote PostgreSQL database...")
    
    # Initialize the SQL tool
    sql_tool = SQLQueryTool(config=TEST_CONFIG)
    sql_tool.initialize()
    
    if not sql_tool.engine:
        logger.error("Failed to initialize SQL engine")
        return False
    
    # Test queries
    test_queries = [
        {
            "name": "List all staff",
            "query": "Tüm personeli listele",
            "description": "Turkish query to list all staff members"
        },
        {
            "name": "Computer Science staff",
            "query": "Show me all staff in Computer Science department",
            "description": "English query for specific department"
        },
        {
            "name": "Department budgets",
            "query": "2024 yılı bütçelerini göster",
            "description": "Turkish query for 2024 budgets"
        },
        {
            "name": "Active facilities",
            "query": "What facilities are currently active?",
            "description": "English query for active facilities"
        },
        {
            "name": "Upcoming events",
            "query": "Yaklaşan etkinlikleri listele",
            "description": "Turkish query for upcoming events"
        },
        {
            "name": "High salary staff",
            "query": "Show staff with salary greater than 10000",
            "description": "English query with condition"
        }
    ]
    
    success_count = 0
    total_tests = len(test_queries)
    
    for i, test in enumerate(test_queries, 1):
        logger.info(f"\n--- Test {i}/{total_tests}: {test['name']} ---")
        logger.info(f"Description: {test['description']}")
        logger.info(f"Query: {test['query']}")
        
        try:
            result = await sql_tool.execute(test['query'])
            
            if result.get('success', False):
                logger.info(f"✅ Success! Generated SQL: {result.get('query', 'N/A')}")
                logger.info(f"Results count: {result.get('count', 0)}")
                
                # Show first few results
                results = result.get('results', [])
                if results:
                    logger.info("Sample results:")
                    for j, row in enumerate(results[:3]):  # Show first 3 rows
                        logger.info(f"  Row {j+1}: {row}")
                    if len(results) > 3:
                        logger.info(f"  ... and {len(results) - 3} more rows")
                else:
                    logger.info("No results returned")
                
                success_count += 1
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"❌ Exception occurred: {str(e)}")
        
        logger.info("-" * 50)
    
    logger.info(f"\n=== Test Summary ===")
    logger.info(f"Successful tests: {success_count}/{total_tests}")
    logger.info(f"Success rate: {(success_count/total_tests)*100:.1f}%")
    
    return success_count == total_tests

async def test_direct_sql():
    """Test direct SQL execution without LLM conversion."""
    logger.info("\n=== Testing Direct SQL Execution ===")
    
    sql_tool = SQLQueryTool(config=TEST_CONFIG)
    sql_tool.initialize()
    
    if not sql_tool.engine:
        logger.error("Failed to initialize SQL engine")
        return False
    
    # Direct SQL queries
    direct_queries = [
        {
            "name": "Count staff",
            "sql": "SELECT COUNT(*) as total_staff FROM staff",
            "description": "Count total staff members"
        },
        {
            "name": "Department summary",
            "sql": "SELECT name, head, budget FROM departments ORDER BY budget DESC",
            "description": "List departments by budget"
        },
        {
            "name": "Staff by department",
            "sql": "SELECT department, COUNT(*) as staff_count FROM staff GROUP BY department ORDER BY staff_count DESC",
            "description": "Staff count by department"
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(direct_queries, 1):
        logger.info(f"\n--- Direct SQL Test {i}: {test['name']} ---")
        logger.info(f"Description: {test['description']}")
        logger.info(f"SQL: {test['sql']}")
        
        try:
            result = await sql_tool.execute("", sql_query=test['sql'], use_llm=False)
            
            if result.get('success', False):
                logger.info(f"✅ Success! Results count: {result.get('count', 0)}")
                
                results = result.get('results', [])
                if results:
                    logger.info("Results:")
                    for row in results:
                        logger.info(f"  {row}")
                
                success_count += 1
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"❌ Exception occurred: {str(e)}")
    
    logger.info(f"\nDirect SQL tests: {success_count}/{len(direct_queries)} successful")
    return success_count == len(direct_queries)

def check_database_connection():
    """Check if PostgreSQL database is accessible."""
    logger.info("Checking PostgreSQL database connection...")
    
    try:
        from sqlalchemy import create_engine, text
        engine = create_engine(TEST_CONFIG["connection_string"])
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ PostgreSQL connection successful!")
            logger.info(f"Database version: {version}")
            
            # Check if tables exist
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            logger.info(f"Available tables: {', '.join(tables)}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        logger.error("Make sure PostgreSQL is running and accessible at localhost:5432")
        logger.error("You can start it with: docker-compose -f tests/docker-compose-postgres.yml up -d")
        return False

async def main():
    """Main test function."""
    logger.info("=== Remote SQL Database Test ===")
    
    # Check database connection first
    if not check_database_connection():
        logger.error("Database connection failed. Exiting.")
        sys.exit(1)
    
    # Run tests
    try:
        # Test natural language to SQL conversion
        nl_success = await test_sql_tool()
        
        # Test direct SQL execution
        direct_success = await test_direct_sql()
        
        # Overall result
        if nl_success and direct_success:
            logger.info("\n🎉 All tests passed! Remote SQL database is working correctly.")
            logger.info("You can now use this configuration in your admin_bot.yaml file:")
            logger.info(f"connection_string: {TEST_CONFIG['connection_string']}")
        else:
            logger.error("\n❌ Some tests failed. Please check the logs above.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
