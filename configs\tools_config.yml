primary_agent:
  llm: gpt-4o-mini
  llm_temperature: 0.0

mongodb_uri: "mongodb+srv://username:<EMAIL>"
database_name: "rag_vectordb"
collection_name: "rag_collection"

swiss_airline_policy_rag:
  unstructured_docs: "data/unstructured_docs/swiss_airline_policy"
  vectordb: "data/airline_policy_vectordb"
  collection_name: rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

stories_rag:
  unstructured_docs: "data/unstructured_docs/stories"
  vectordb: "data/stories_vectordb"
  collection_name: stories-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

################################
    # is_guvenligi
    # senato_kararlari
    # universite_puanlari
    # usul_ve_esaslar
    # yok_yazilari
    # yonergeler
    # yonetmelikler
    # is_sagligi
################################

is_guvenligi_rag:
  unstructured_docs: "data/unstructured_docs/is_guvenligi"
  vectordb: "data/is_guvenligi_vectordb"
  collection_name: is_guvenligi-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

senato_kararlari_rag:
  unstructured_docs: "data/unstructured_docs/senato_kararlari"
  vectordb: "data/senato_kararlari_vectordb"
  collection_name: senato_kararlari-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

universite_puanlari_rag:
  unstructured_docs: "data/unstructured_docs/universite_puanlari"
  vectordb: "data/universite_puanlari_vectordb"
  collection_name: universite_puanlari-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

usul_ve_esaslar_rag:
  unstructured_docs: "data/unstructured_docs/usul_ve_esaslar"
  vectordb: "data/usul_ve_esaslar_vectordb"
  collection_name: usul_ve_esaslar-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

yok_yazilari_rag:
  unstructured_docs: "data/unstructured_docs/yok_yazilari"
  vectordb: "data/yok_yazilari_vectordb"
  collection_name: yok_yazilari-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

yonergeler_rag:
  unstructured_docs: "data/unstructured_docs/yonergeler"
  vectordb: "data/yonergeler_vectordb"
  collection_name: yonergeler-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2

yonetmelikler_rag:
  unstructured_docs: "data/unstructured_docs/yonetmelikler"
  vectordb: "data/yonetmelikler_vectordb"
  collection_name: yonetmelikler-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2


is_sagligi_rag:
  unstructured_docs: "data/unstructured_docs/is_sagligi"
  vectordb: "data/is_sagligi_vectordb"
  collection_name: is_sagligi-rag-chroma
  llm: gpt-4o-mini
  llm_temperature: 0.0
  embedding_model: text-embedding-3-small
  chunk_size: 500
  chunk_overlap: 100
  k: 2







travel_sqlagent_configs:
  travel_sqldb_dir: "data/travel.sqlite"
  llm: "gpt-3.5-turbo"
  llm_temperature: 0.0
  
chinook_sqlagent_configs:
  chinook_sqldb_dir: "data/Chinook.db"
  llm: "gpt-3.5-turbo"
  llm_temperature: 0.0

langsmith:
  tracing: "true"
  project_name: "rag_sqlagent_project"

tavily_search_api:
  tavily_search_max_results: 2

graph_configs:
  thread_id: 1 # This can be adjusted to assign a unique value for each user session, so it's easier to access data later on.
  