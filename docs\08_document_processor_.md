# Chapter 8: Document Processor

Welcome back! In the previous chapter, [<PERSON><PERSON> Configuration](07_bot_configuration_.md), we learned how we use simple YAML files to define different specialized bots, specifying which tools they have and how their AI brains are configured. This gives us flexibility in creating various bots like an "AcademicBot" or a "StudentBot".

However, a crucial piece is still missing for bots that need to answer questions based on *your specific documents*. How do you get the information from your company's internal guides, your research papers, or your course materials *into* the system so that, say, a [Document Search Tool](04_tool_.md) can find it?

## What Problem Does the Document Processor Solve?

Imagine you have a folder full of PDF files containing important information – maybe manuals, reports, or policies. You want your bot to be able to read these files and answer questions about their content.

The challenge is that computers don't inherently understand the text and structure within a PDF or Word document in a way that allows for instant, semantic searching. A simple keyword search might miss important information. To truly answer questions intelligently, the system needs to:

1.  **Read** the content from various file types (PDFs, DOCXs, TXTs).
2.  Break down **large documents** into smaller, manageable pieces.
3.  Understand the **meaning** of each piece of text.
4.  Store these meaningful pieces in a way that allows for **quick lookups** based on what the user is asking about, not just exact keywords.

The problem is: **How do we prepare our raw documents so that they can be efficiently searched by a Document Search Tool?**

This is the job of the **Document Processor**.

Think of the **Document Processor** as a **specialized librarian and indexer**. You give it raw books (documents), and it reads them, breaks them down into chapters and paragraphs (chunks), understands the main ideas in each part (creates embeddings), and organizes them neatly in a searchable card catalog (a vector database, like Chroma). This way, when someone asks a question, the librarian (the [Document Search Tool](04_tool_.md)) can quickly find the most relevant sections in the catalog.

It's the essential first step to enabling a RAG (Retrieval Augmented Generation) system for your private document collection.

## Key Steps of Document Processing

The Document Processor performs several key steps to get your documents ready for searching:

1.  **Loading:** It reads the content from your files (PDF, DOCX, TXT). It uses different "loaders" depending on the file type.
2.  **Splitting:** Large documents are broken down into smaller `chunks` of text. This is important because Large Language Models (LLMs) and vector databases have limits on how much text they can process or store at once. Splitting ensures we work with manageable pieces.
3.  **Embedding:** Each text chunk is converted into a numerical representation called an `embedding`. Think of an embedding as a string of numbers that captures the *meaning* of the text. Text chunks with similar meanings will have embeddings that are numerically "close" to each other. This step requires an embedding model (like one from OpenAI).
4.  **Storing:** The original text chunks *and* their newly created embeddings are stored in a special type of database called a **vector store**. In this project, we use **Chroma** as the vector store. Chroma is designed to allow very fast "similarity search" – finding text chunks whose embeddings are closest to the embedding of the user's search query.

Each set of documents you process for a specific topic or purpose is usually stored in a dedicated space within the vector store, often called a "collection". This allows a [Document Search Tool](04_tool_.md) configured for "Academic Documents" to only search the "Academic Documents" collection, separate from a "Policy Documents" collection.

## How to Use the Document Processor (From the Outside)

The Document Processor is typically used "out of band" from the main query API. You don't usually process documents *while* answering a question. Instead, you process your documents *ahead of time*, building the searchable database.

In the `atlas-q-a-rag` project, the `DocumentProcessor` class (`app/document_processing/document_processor.py`) provides methods to handle this. While the direct API endpoints to trigger these might be defined in `app/main.py` (often under `/process` routes), the core logic is within the `DocumentProcessor` class itself.

The two main methods you'd interact with (likely via an API call or script) are:

*   `process_document(file_path, collection_name, ...)`: To process a single file.
*   `process_directory(directory_path, collection_name, ...)`: To process all supported files in a folder (and optionally its subfolders).

When you use these methods, you need to provide:

*   The location of the document(s) (`file_path` or `directory_path`).
*   The `collection_name` where you want to store the processed information. This name is how your [Document Search Tool](04_tool_.md) will find the data later.

For example, you might trigger a process that says: "Process all PDFs in `./my_reports/` and put them into a collection called `company_reports`."

```python
# Conceptual example - not actual API call code
# Imagine you have an endpoint or script calling this:

from app.document_processing import DocumentProcessor

# Initialize the processor (using default settings from config)
processor = DocumentProcessor() # simplified init

# Process a directory of reports
results = processor.process_directory(
    directory_path="./data/raw/company_reports",
    collection_name="company_reports"
)

print(f"Processed reports: {results['successful_count']} successful")
# Output might look like: Processed reports: 15 successful
```

This conceptual example shows that you provide the *input location* and the *output destination name* (the collection name), and the `DocumentProcessor` handles the rest of the steps (loading, splitting, embedding, storing).

## Under the Hood: The Processing Flow

Let's look at the basic steps the `DocumentProcessor` takes when you ask it to process a single document:

```mermaid
sequenceDiagram
    participant UserAdmin as User/Admin (Trigger)
    participant DocProcessor as Document Processor
    participant FileSystem as File System
    participant Loader as Document Loader
    participant Splitter as Text Splitter
    participant Embeddings as Embedding Model
    participant Chroma as Chroma Vector Store

    UserAdmin->>DocProcessor: process_document(file_path, collection_name, ...)
    DocProcessor->>FileSystem: Access file at file_path
    DocProcessor->>Loader: Get loader for file type (e.g., PyPDFLoader)
    Loader->>FileSystem: Read file content
    FileSystem-->>Loader: Raw text/structure
    Loader-->>DocProcessor: Loaded Document object(s)
    DocProcessor->>Splitter: split_documents(documents)
    Splitter-->>DocProcessor: List of Chunks
    DocProcessor->>Embeddings: Create embeddings for each Chunk
    Embeddings-->>DocProcessor: Embeddings (numerical vectors)
    DocProcessor->>Chroma: Add Chunks and Embeddings to collection_name
    Chroma-->>DocProcessor: Confirmation/Result
    DocProcessor-->>UserAdmin: Processing Results
```

This flow shows how the processor orchestrates the different sub-tasks: reading the file content using the correct loader, splitting it, creating embeddings, and finally adding everything to the Chroma vector store under the specified collection name.

## Under the Hood: The DocumentProcessor Class

Let's look at some key parts of the `app/document_processing/document_processor.py` file.

### Initialization

The `DocumentProcessor` is initialized with settings that control how it operates.

```python
# File: app\document_processing\document_processor.py (Simplified)

class DocumentProcessor:
    SUPPORTED_EXTENSIONS = {
        ".pdf": PyPDFLoader,
        ".docx": Docx2txtLoader,
        ".doc": UnstructuredWordDocumentLoader,
        ".txt": TextLoader,
    }

    def __init__(
        self,
        data_dir: str = "data", # Base directory for processing output
        embedding_model: str = "text-embedding-3-small", # Which embedding model to use
        chunk_size: int = 1000, # Size of chunks
        chunk_overlap: int = 200, # Overlap between chunks
    ):
        self.data_dir = Path(data_dir)
        self.embedding_model = embedding_model
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Initialize components needed for processing
        self.embeddings = OpenAIEmbeddings(model=embedding_model) # The embedding model instance
        self.text_splitter = RecursiveCharacterTextSplitter( # The splitter instance
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            # ... other splitter settings ...
        )

        # ... metadata tracking setup ...

    # ... other methods ...
```

**Explanation:**

*   `SUPPORTED_EXTENSIONS`: This class variable lists which file types the processor knows how to handle and which LangChain `Loader` class to use for each.
*   `__init__`: The constructor takes parameters like `data_dir` (where to store outputs like the Chroma database), `embedding_model` (which AI model to use for creating embeddings), and settings for `chunk_size` and `chunk_overlap`.
*   It initializes the necessary LangChain components: `OpenAIEmbeddings` (using the specified model) and `RecursiveCharacterTextSplitter` (using the chunking settings). These instances will be used in the processing steps.

### Getting the Correct Loader

The processor needs to know how to read different file types.

```python
# File: app\document_processing\document_processor.py (Simplified)

    def _get_document_loader(self, file_path: Path) -> Optional[Any]:
        """
        Get appropriate document loader for file type.
        """
        extension = file_path.suffix.lower() # Get the file extension
        loader_class = self.SUPPORTED_EXTENSIONS.get(extension) # Look up the loader class

        if not loader_class:
            logger.warning(f"Unsupported file type: {extension}")
            return None

        try:
            # Create an instance of the correct loader class
            # Pass the file path to the loader
            if extension == ".txt": # Special case for text encoding
                 return loader_class(str(file_path), encoding="utf-8")
            else:
                 return loader_class(str(file_path))
        except Exception as e:
            logger.error(f"Error creating loader for {file_path}: {e}")
            return None
```

**Explanation:**

*   This helper method takes a file path, checks its extension, and looks up the corresponding loader class in the `SUPPORTED_EXTENSIONS` dictionary.
*   If a loader is found, it creates an instance of that loader class, passing the file path, and returns it. This instance is then used to perform the actual reading (`loader.load()`).

### Processing a Single Document (`process_document`)

This is the main method for handling one file. It combines the steps we discussed.

```python
# File: app\document_processing\document_processor.py (Simplified)

    def process_document(
        self,
        file_path: str,
        collection_name: str, # The name for the Chroma collection
        persist_directory: Optional[str] = None, # Where to store Chroma data
        custom_metadata: Optional[Dict[str, Any]] = None, # Extra info to add
    ) -> Dict[str, Any]:
        # ... file existence check ...

        try:
            # 1. Get and use the correct loader
            loader = self._get_document_loader(file_path)
            documents = loader.load() # Load the document content

            # ... check if content was loaded ...

            # 2. Split documents into chunks
            chunks = self.text_splitter.split_documents(documents)

            # 3. Add metadata to chunks (source, chunk index, etc.)
            for i, chunk in enumerate(chunks):
                chunk.metadata.update({"source_file": str(file_path), ...}) # Add file info
                if custom_metadata:
                    chunk.metadata.update(custom_metadata) # Add any custom info

            # 4. Initialize/Get Chroma vector store for the specified collection
            if not persist_directory: # Use default path if none provided
                persist_directory = str(
                    self.data_dir / "chroma_stores" / collection_name
                )
            vector_store = Chroma(
                collection_name=collection_name, # Connect to this collection
                embedding_function=self.embeddings, # Use our initialized embeddings
                persist_directory=persist_directory, # Use this directory for storage
            )

            # 5. Add chunks (which now include text, metadata, and will get embeddings)
            vector_store.add_documents(chunks)

            # ... update and save processing metadata ...

            return {"success": True, "file_path": str(file_path), "chunk_count": len(chunks), ...}

        except Exception as e:
            # ... error handling and metadata update ...
            return {"success": False, "error": str(e)}
```

**Explanation:**

*   The method takes the `file_path`, `collection_name`, and optional `persist_directory` and `custom_metadata`.
*   It uses `_get_document_loader` to load the file content into LangChain `Document` objects.
*   It passes these `Document` objects to `self.text_splitter.split_documents()` to break them into smaller `chunks`.
*   It iterates through the created `chunks` and adds useful information (like the original `source_file`, a `chunk_index`, etc.) and any provided `custom_metadata` to the `metadata` dictionary of each chunk. This metadata is important because it helps the bot tell the user *where* the information came from later.
*   It initializes or connects to a `Chroma` vector store instance, specifying the `collection_name` and `persist_directory`. This is where the data for this specific collection will be stored. It also tells Chroma to use `self.embeddings` to create the numerical representations.
*   `vector_store.add_documents(chunks)`: This is the core step where LangChain/Chroma takes the list of `chunks`, generates an embedding for each using the `self.embeddings` function, and stores both the text and the embedding in the specified collection.
*   The method also includes logic to track which files have been processed and the result (success/failure) in a metadata file (`processing_metadata.json`).

The `process_directory` method (`app/document_processing/document_processor.py`, not shown here) simply loops through all supported files found in the given directory (optionally including subdirectories) and calls `process_document` for each one, using the same `collection_name` and `persist_directory` for all files in that batch.

## Connecting to the Document Search Tool

Once the `DocumentProcessor` has done its job and created a Chroma collection (e.g., located at `./chroma_db/academic` with the name `academic_documents`), how does the [Document Search Tool](04_tool_.md) use this data?

Remember the [Bot Configuration](07_bot_configuration_.md) chapter? The `bot_config.yaml` file defines the tools available to a bot. For a bot that needs to search academic documents, its configuration would include a `DocumentSearchTool` like this:

```yaml
# Snippet from configs/academic_bot.yaml (Conceptual)

tools:
  - type: DocumentSearchTool
    enabled: true
    config:
      collection_name: academic_documents # <-- Tool knows which collection to use
      persist_directory: ./chroma_db/academic # <-- Tool knows where to find the data
      top_k: 8
      # ... other tool configs ...
```

When the [AgenticRAG System](01_agenticrag_system_.md) initializes the `AcademicBot` during startup, it reads this configuration. It then creates an instance of the `DocumentSearchTool`, passing this `config` dictionary to the tool's `__init__` (and `initialize`) method.

The `DocumentSearchTool` uses the `collection_name` and `persist_directory` from its `config` to connect to the *exact same* Chroma database collection that the `DocumentProcessor` created:

```python
# File: app\tools\document_search.py (Simplified initialize method)

class DocumentSearchTool(BaseTool):
    # ... __init__ calls initialize ...

    def initialize(self) -> None:
        """Initialize the document search tool."""
        self.collection_name = self.config.get("collection_name", "default")
        # ... get other config like embedding_model, top_k ...
        self.persist_directory = self.config.get("persist_directory", "./chroma_db")

        # Initialize vector store - this connects to the EXISTING data
        try:
            self.vector_store = Chroma(
                collection_name=self.collection_name, # Connects to the specified collection
                embedding_function=self.embeddings, # Uses the same embedding model
                persist_directory=self.persist_directory, # Points to the directory
            )
            # ... logging ...
        except Exception as e:
            # ... error handling ...
            self.vector_store = None
```

Then, when the [Query Router](03_query_router_.md) decides to use the `DocumentSearchTool` for a query and calls its `execute` method, the tool uses this `self.vector_store` instance to perform the similarity search:

```python
# File: app\tools\document_search.py (Simplified execute method)

    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for documents relevant to the query.
        """
        if not self.vector_store:
            # ... error handling ...
            return {"success": False, "error": "Vector store not initialized", ...}

        try:
            # Search for documents in the connected collection
            docs = self.vector_store.similarity_search(query, k=kwargs.get("top_k", self.top_k))

            # ... format results ...
            return {"success": True, "documents": results, ...}
        except Exception as e:
            # ... error handling ...
            return {"success": False, "error": str(e), "documents": []}
```

This completes the loop: the `DocumentProcessor` prepares the data and stores it in a named collection, and the `DocumentSearchTool` for a specific bot is configured to connect to that exact collection when it needs to retrieve information.

## Document Processing Configuration

While the bot configuration defines *if* a bot uses the `DocumentSearchTool` and *which collection* it points to, there's often a separate configuration file (`configs/document_processing.yaml`) that defines settings *for the Document Processor itself*.

This file might specify default `chunk_size`, `chunk_overlap`, the default `embedding_model`, and define specific `collections` with their own settings (like `academic_documents` with a larger chunk size than `student_documents`). This allows you to manage document processing settings independently from individual bot definitions. The Document Processor component reads this file on startup.

## Conclusion

In this chapter, we introduced the **Document Processor**. We learned that it's the system component responsible for taking raw documents (like PDFs, DOCXs, TXTs) and preparing them for efficient searching by a [Document Search Tool](04_tool_.md). This involves loading content, splitting it into chunks, creating numerical embeddings for those chunks, and storing them in a searchable database like Chroma, often organized into collections by topic or source. This process is typically done ahead of time, making your custom document content accessible to your bots and enabling powerful RAG capabilities.

We have now covered all the major conceptual components that make up the `atlas-q-a-rag` system, from the top-level manager to the tools and the brain that generates answers, including how they handle memory and how their data sources like documents are prepared.

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)