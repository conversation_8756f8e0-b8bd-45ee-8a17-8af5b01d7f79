services:
  # Agentic RAG Backend
  agentic-rag:
    build:
      context: .
      dockerfile: Dockerfile.backend-only
      target: production
    container_name: agentic-rag-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=true
      - LOG_LEVEL=INFO
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs:ro
      - ./prompts:/app/prompts:ro
    network_mode: "host" 
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
