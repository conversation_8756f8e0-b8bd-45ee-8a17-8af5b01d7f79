# Chapter 5: Agent (LangGraphAgent)

Welcome back! In the previous chapters, we've built up our understanding of the [AgenticRAG System](01_agenticrag_system_.md) (the manager), the [<PERSON><PERSON>](02_bot_.md) (the specialized assistant), the [Query Router](03_query_router_.md) (the smart receptionist deciding when to use tools), and the [Tool](04_tool_.md) (the workers fetching external information).

Now, we arrive at the core "brain" of each [Bot](02_bot_.md): the **Agent**. Specifically, in this project, we use a `LangGraphAgent`, leveraging the powerful LangGraph framework to structure the Agent's logic.

## What Problem Does the Agent Solve?

Imagine our "AcademicBot" has just received the results from the [Query Router](03_query_router_.md). These results might include snippets of text found by the [Document Search Tool](04_tool_.md), maybe some data fetched by a [Database Tool](04_tool_.md), and perhaps the conversation history from previous turns with the user ([Memory Management](06_memory_management_.md)).

The problem is: **How does the system take the original user question, the results from any tools that were used, and the conversation history, and weave it all together into a single, coherent, and helpful answer for the user?**

This is the job of the **Agent**.

Think of the **Agent** as the **synthesizer** or the **editor** of the Bot. It receives all the raw materials – the user's request, facts from tools, and memory of the past conversation – and uses its intelligence (provided by a Language Model like GPT-4) to:

1.  Understand the user's original intent in light of the history.
2.  Analyze the information provided by the [Tool](04_tool_.md)s.
3.  Combine the relevant pieces of information.
4.  Format the final answer in a clear, concise, and appropriate tone (guided by the Bot's prompts).

It's the ultimate step where the Bot's knowledge and retrieved information are transformed into the final response the user sees.

## Why Use LangGraph?

LangGraph is a library built on top of LangChain that helps you build "stateful" multi-actor applications with LLMs. In simpler terms, it lets you define a series of steps or a flowchart for your AI agent to follow.

Instead of just having the AI think once, LangGraph allows us to define a sequence like:

*   Step 1: Process tool results and gather context.
*   Step 2: Generate the final response using context, query, and history.

This structured approach makes the agent's logic more predictable and easier to build and debug compared to a single, large prompt that tries to do everything at once. Our `LangGraphAgent` uses a simple, linear graph (flowchart) for this purpose.

## Where Does the Agent Fit in the Flow?

The Agent is the last step in the processing pipeline *within* a [Bot](02_bot_.md) before the response is sent back to the user. The [AgenticRAG System](01_agenticrag_system_.md) calls the [Query Router](03_query_router_.md) first, and *then* passes the router's output (including tool results) to the Agent.

Here's the simplified flow again, highlighting the Agent:

```mermaid
sequenceDiagram
    participant AgenticRAG as AgenticRAG System
    participant BotC as Bot's Components
    participant QueryRouter as Query Router
    participant Tool as Tool
    participant Agent as Agent (LangGraphAgent)
    participant Memory as Memory Manager

    AgenticRAG->>BotC: Get specific Bot's parts
    AgenticRAG->>QueryRouter: route_query(user_query, ...)
    alt Tools Selected
        QueryRouter->>Tool: Execute Tool(s)
        Tool-->>QueryRouter: Tool Results
    end
    QueryRouter-->>AgenticRAG: Tool Selection & Results
    AgenticRAG->>Agent: process_query(user_query, tool_results, session_id)
    Agent->>Memory: Get Chat History (if session_id exists)
    Memory-->>Agent: Chat History
    Agent->>Agent: Use tool results, query, history, prompts
    Agent->>Agent: Synthesize final response (via LLM)
    Agent->>Memory: Save Query & Response (if session_id exists)
    Agent-->>AgenticRAG: Final Response & Metadata
    AgenticRAG-->>FastAPIA: QueryResponse
```

Notice that the Agent interacts with the [Memory Management](06_memory_management_.md) system to retrieve past conversation turns and to save the current turn. This allows the Bot to remember context across multiple user queries.

## Using the Agent (Code Snippet)

Just like the [Query Router](03_query_router_.md), you don't directly call the `LangGraphAgent` from outside the system. The [AgenticRAG System](01_agenticrag_system_.md) handles this call as part of its `process_query` method.

Let's look at the relevant part in `app/core/agentic_rag.py` again:

```python
# File: app\core\agentic_rag.py (simplified)

    async def process_query(
        self, bot_name: str, request: QueryRequest
    ) -> QueryResponse:
        # ... get the bot components ...

        try:
            # 1. Route the query using the bot's query router
            query_router: QueryRouter = bot["query_router"]
            tool_results = await query_router.route_query(
                request.query, **request.metadata or {}
            )

            # 2. Process the query and tool results with the bot's agent
            agent: LangGraphAgent = bot["agent"] # Get the agent instance
            agent_response = await agent.process_query( # Call its process_query method
                request.query,
                tool_results["tool_responses"], # Pass results from router
                session_id=request.session_id, # Pass session for memory
            )

            # 3. Format and return response using results from agent and router
            # ... formatting ...

            response = QueryResponse(
                # ... other fields ...
                response=agent_response["response"], # Get the final text from agent
                # ... other fields ...
            )

            return response
```

**Explanation:**

*   `agent = bot["agent"]`: The code retrieves the specific `LangGraphAgent` instance that was initialized for this particular `bot_name` when the system started.
*   `agent_response = await agent.process_query(...)`: This is the key call! The user's original query (`request.query`), the results gathered by the [Query Router](03_query_router_.md) (`tool_results["tool_responses"]`), and the `session_id` (for [Memory Management](06_memory_management_.md)) are all passed to the agent's `process_query` method.
*   `agent_response`: This variable holds the output from the agent, which is a dictionary containing the final generated `response` text and potentially error information.
*   `response=agent_response["response"]`: The final text generated by the Agent is then used to populate the `response` field of the `QueryResponse` object sent back to the user.

This shows that the `process_query` method on the `LangGraphAgent` is the main way the rest of the system interacts with the Agent's logic.

## Under the Hood: How the LangGraphAgent Works

Let's peek into the `app/agents/langgraph_agent.py` file to see the core parts of the `LangGraphAgent`.

### Initialization (`__init__`)

The Agent is set up when the [AgenticRAG System](01_agenticrag_system_.md) loads the [Bot](02_bot_.md)s.

```python
# File: app\agents\langgraph_agent.py (simplified)

class LangGraphAgent:
    def __init__(
        self, agent_config: AgentConfig, system_prompt: str, query_prompt: str
    ):
        # Store config and prompts
        self.agent_config = agent_config
        self.system_prompt = system_prompt # System instructions
        self.query_prompt = query_prompt   # Query handling instructions

        # Initialize the LLM that the agent will use
        self.llm = ChatOpenAI(
            model=agent_config.model, # Model specified in bot config
            temperature=agent_config.config.get("temperature", 0.0),
        )

        # Initialize memory manager (we'll cover this in Chapter 6)
        self.memory_manager = MemoryManager()

        # Build the LangGraph graph (the flowchart)
        self.graph = self._build_graph()

    # ... other methods ...
```

**Explanation:**

*   The constructor takes the `AgentConfig` from the [Bot Configuration](07_bot_configuration_.md) YAML file, plus the `system_prompt` and `query_prompt` text loaded from files.
*   It initializes the specific LLM (`self.llm`) that this Agent will use, based on the `model` specified in the config.
*   It initializes a `MemoryManager` instance to handle conversation history.
*   Crucially, it calls `_build_graph()` to set up the LangGraph workflow.

### Building the Graph (`_build_graph`)

This method defines the sequence of steps for the Agent.

```python
# File: app\agents\langgraph_agent.py (simplified)

    def _build_graph(self) -> StateGraph:
        """
        Build the LangGraph agent graph (the flowchart).
        """
        # Define the 'state' - what information is carried between steps
        # AgentState includes query, tool_results, context, response, etc.

        # Define the nodes (the steps in the flowchart)
        def retrieve_context(state: AgentState) -> AgentState:
            """Step 1: Process tool results and put relevant info into 'context'."""
            # ... logic to extract content from different tool_results types ...
            # Example: loop through state["tool_results"], format documents, DB results, etc.
            # Update state["context"] with the formatted information
            logger.info("Running retrieve_context node")
            return {**state, "context": processed_context} # Simplified

        def generate_response(state: AgentState) -> AgentState:
            """Step 2: Use context, query, and history to generate response."""
            # ... logic to format context, query, and chat history into a prompt ...
            # ... call self.llm.invoke(...) with the prompt ...
            # Update state["response"] with the LLM's output
            logger.info("Running generate_response node")
            final_response_text = self.llm.invoke(...).content # Simplified
            return {**state, "response": final_response_text} # Simplified

        # Create the graph (the flowchart structure)
        graph = StateGraph(AgentState)

        # Add the nodes (define the steps)
        graph.add_node("retrieve_context", retrieve_context)
        graph.add_node("generate_response", generate_response)

        # Add the edges (define the flow between steps)
        graph.add_edge("retrieve_context", "generate_response") # After retrieve_context, go to generate_response
        graph.add_edge("generate_response", END) # After generate_response, the flow ends

        # Set the starting point
        graph.set_entry_point("retrieve_context")

        # Compile the graph to make it ready to run
        return graph.compile()
```

**Explanation:**

*   `AgentState`: This defines the structure of the data that flows through the graph. It holds things like the `query`, `tool_results`, `context` (the extracted and formatted information from tools), and the final `response`.
*   Nodes (`retrieve_context`, `generate_response`): These are Python functions that represent steps in the workflow. Each node takes the current `state` as input, performs some action, and returns the updated `state`.
    *   `retrieve_context`: This node's job is to take the raw `tool_results` dictionary (passed from the [Query Router](03_query_router_.md)) and extract the useful text content from it, formatting it into a list of `context` items that the LLM can easily use. It handles parsing results from different tool types like `DocumentSearchTool`, `MongoDBQueryTool`, etc.
    *   `generate_response`: This node takes the `query`, the newly formatted `context`, and the `chat_history` (retrieved via the Memory Manager) and puts them all together into a well-structured prompt. It then sends this prompt to the `self.llm` to get the final answer text.
*   Graph Structure: `StateGraph` defines the overall flow.
    *   `add_node`: Adds the steps (`retrieve_context`, `generate_response`).
    *   `add_edge`: Defines the sequence. Here, it's a simple linear path: `retrieve_context` -> `generate_response` -> `END` (meaning the process is finished).
    *   `set_entry_point`: Specifies where the graph starts.
    *   `compile()`: Finalizes the graph structure.

This built graph (`self.graph`) is what the `process_query` method will execute.

### Processing a Query (`process_query`)

This is the method called by the [AgenticRAG System](01_agenticrag_system_.md). It prepares the initial information and runs the graph.

```python
# File: app\agents\langgraph_agent.py (simplified)

    async def process_query(
        self, query: str, tool_results: Dict[str, Any], session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a query using the LangGraph agent by running the graph.
        """
        try:
            # 1. Get chat history using the MemoryManager
            chat_history = None
            if session_id:
                chat_history = self.memory_manager.get_chat_history_str(session_id)
                logger.info(f"Retrieved chat history for session {session_id}") # Log history retrieval

            # 2. Initialize the state for the graph
            initial_state: AgentState = {
                "query": query,
                "context": [],  # This will be filled by the 'retrieve_context' node
                "tool_results": tool_results, # Tool results from the Router
                "response": None, # This will be filled by the 'generate_response' node
                "error": None,
                "chat_history": chat_history, # Pass history into the state
            }

            # 3. Run the LangGraph graph!
            # The graph will execute retrieve_context then generate_response
            result = await self.graph.invoke(initial_state)

            # 4. Save the user query and agent's response to memory
            if session_id:
                self.memory_manager.add_user_message(session_id, query)
                if result["response"]:
                    self.memory_manager.add_ai_message(session_id, result["response"])
                    logger.info(f"Updated conversation memory for session {session_id}") # Log history update

            # 5. Return the final response and any errors
            return {
                "query": query,
                "response": result["response"], # Get the final text from the state
                "error": result["error"],
            }
        except Exception as e:
            logger.error(f"Error processing query in LangGraphAgent: {str(e)}")
            return {
                "query": query,
                "response": "I'm sorry, I encountered an internal error.",
                "error": str(e),
            }

```

**Explanation:**

*   The method receives the user's `query`, the `tool_results` dictionary from the [Query Router](03_query_router_.md), and the `session_id`.
*   It uses the `session_id` to fetch the past `chat_history` from the `MemoryManager`.
*   It creates the `initial_state` dictionary, which is the starting point for the LangGraph. This state contains the original query, the tool results, and the chat history. `context` and `response` are initialized as empty/None because they will be filled by the graph nodes.
*   `result = await self.graph.invoke(initial_state)`: This is the core line that executes the LangGraph workflow. It starts at the entry point (`retrieve_context`), passes the `initial_state` to it, the node updates the state and returns it, the graph passes the updated state to the next node (`generate_response`), and so on, until it reaches `END`. The final state after the graph finishes is stored in `result`.
*   After the graph finishes, the user's query and the agent's final response are saved to memory using the `MemoryManager`, again using the `session_id`.
*   Finally, the method returns the `response` and `error` fields from the final state.

This method orchestrates the entire Agent process: getting history, setting up the initial data, running the defined workflow (the LangGraph), and saving the interaction to memory.

## Conclusion

In this chapter, we learned that the **Agent (LangGraphAgent)** is the intelligent core within each [Bot](02_bot_.md). It acts as a synthesizer, taking the user's query, the results gathered by the [Tool](04_tool_.md)s (orchestrated by the [Query Router](03_query_router_.md)), and the conversation history ([Memory Management](06_memory_management_.md)), and using a Language Model (LLM) to generate the final, coherent answer. It uses LangGraph to structure this process into a sequence of steps for clarity and control. You interact with the Agent indirectly via the [AgenticRAG System](01_agenticrag_system_.md)'s `process_query` method.

Now that we've covered how the Agent uses conversation history, let's dedicate the next chapter to understanding the **Memory Management** component in more detail.

[Memory Management](06_memory_management_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)