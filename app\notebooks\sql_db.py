import os
import pandas as pd
from sqlalchemy import create_engine, text

server = "10.1.1.83"
database = "VERSISDB"
username = "ai-read"
password = "CHXG7jS2y9X4"
driver = "ODBC Driver 17 for SQL Server"

connection_string = (
    f"mssql+pyodbc://{username}:{password}@{server}/{database}"
    f"?driver={driver.replace(' ', '+')}&TrustServerCertificate=yes"
)

engine = create_engine(connection_string)

all_dfs = []

with engine.connect() as conn:
    tables = conn.execute(
        text(
            """
        SELECT TABLE_SCHEMA, TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
    """
        )
    ).fetchall()

    for schema, table in tables:
        full_table_name = f"{schema}.{table}"
        query = f"SELECT * FROM [{schema}].[{table}]"
        try:
            df = pd.read_sql(query, conn)
            df["_source_table"] = full_table_name  # hangi tablodan geldiği bilgisi
            all_dfs.append(df)
            print(f"✅ {full_table_name} yüklendi.")
        except Exception as e:
            print(f"❌ {full_table_name} yüklenemedi: {e}")

# Tüm DataFrame'leri alt alta birleştir (kolonlar farklı olabilir)
combined_df = pd.concat(all_dfs, ignore_index=True, sort=False)

# CSV olarak kaydet
combined_df.to_csv("versisdb.csv", index=False, encoding="utf-8-sig")
print("Tüm veriler 'tum_veritabani.csv' dosyasına kaydedildi.")
