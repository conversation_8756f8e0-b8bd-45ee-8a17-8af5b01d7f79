#!/usr/bin/env python3
"""
Check the schema of the test database.
"""

import sqlite3
import sys
from pathlib import Path

def check_database_schema(db_path):
    """Check and display the database schema."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"=== Database Schema for {db_path} ===\n")
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"Found {len(tables)} tables:")
        for table in tables:
            print(f"- {table[0]}")
        
        print("\n" + "="*50)
        
        # Get schema for each table
        for table in tables:
            table_name = table[0]
            print(f"\nTable: {table_name}")
            print("-" * 30)
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("Columns:")
            for col in columns:
                col_id, name, data_type, not_null, default_val, pk = col
                pk_marker = " (PRIMARY KEY)" if pk else ""
                not_null_marker = " NOT NULL" if not_null else ""
                default_marker = f" DEFAULT {default_val}" if default_val else ""
                print(f"  - {name}: {data_type}{not_null_marker}{default_marker}{pk_marker}")
            
            # Get sample data
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"Row count: {count}")
            
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_rows = cursor.fetchall()
                print("Sample data:")
                for i, row in enumerate(sample_rows, 1):
                    print(f"  Row {i}: {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error checking database: {str(e)}")
        return False
    
    return True

def main():
    db_path = Path(__file__).parent / "test_db.sqlite"
    
    if not db_path.exists():
        print(f"Database file not found: {db_path}")
        sys.exit(1)
    
    success = check_database_schema(str(db_path))
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
