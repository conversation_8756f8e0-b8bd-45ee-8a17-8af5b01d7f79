# API Keys - Replace with your actual API keys
OPENAI_API_KEY=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# Database Configuration (automatically set in Docker)
MONGODB_URL=mongodb://localhost:27017/

# SQL Database Configuration
# For SQLite (local file)
SQL_CONNECTION_STRING=sqlite:///./tests/test_db.sqlite

# For PostgreSQL (remote)
# SQL_CONNECTION_STRING=postgresql://username:password@hostname:port/database_name
# Example: SQL_CONNECTION_STRING=postgresql://myuser:mypassword@localhost:5432/mydatabase

# For MySQL (remote)
# SQL_CONNECTION_STRING=mysql+pymysql://username:password@hostname:port/database_name
# Example: SQL_CONNECTION_STRING=mysql+pymysql://myuser:mypassword@localhost:3306/mydatabase

# For SQL Server (remote)
# SQL_CONNECTION_STRING=mssql+pyodbc://username:password@hostname:port/database_name?driver=ODBC+Driver+17+for+SQL+Server
# Example: SQL_CONNECTION_STRING=mssql+pyodbc://myuser:mypassword@localhost:1433/mydatabase?driver=ODBC+Driver+17+for+SQL+Server

# Optional Gradio Configuration
GRADIO_SERVER_NAME=0.0.0.0
GRADIO_SERVER_PORT=7860
