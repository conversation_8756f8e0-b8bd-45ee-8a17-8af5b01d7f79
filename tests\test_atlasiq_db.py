#!/usr/bin/env python
"""
Test script for AtlasIQ database.
"""

import os
import sys
import sqlite3
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Database file path
DB_PATH = "atlasiq_test_db.sqlite"


def test_atlasiq_database():
    """Test the AtlasIQ database by running sample queries."""
    
    if not os.path.exists(DB_PATH):
        logger.error(f"Database {DB_PATH} not found. Please run create_atlasiq_db.py first.")
        return False
    
    logger.info(f"Testing AtlasIQ database: {DB_PATH}")
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Test queries
        test_queries = [
            ("Count institutions", "SELECT COUNT(*) as total_institutions FROM institutions"),
            ("Count researchers", "SELECT COUNT(*) as total_researchers FROM researchers"),
            ("Count grants", "SELECT COUNT(*) as total_grants FROM grants"),
            ("Count publications", "SELECT COUNT(*) as total_publications FROM publications"),
            ("Turkish institutions", "SELECT name, city FROM institutions WHERE country = 'Türkiye'"),
            ("Atlas University researchers", "SELECT r.name, r.title, r.research_areas FROM researchers r JOIN institutions i ON r.institution_id = i.id WHERE i.name = 'Atlas Üniversitesi'"),
            ("Active grants", "SELECT title, funding_agency, amount, currency FROM grants WHERE status = 'Active' LIMIT 5"),
            ("Recent publications", "SELECT title, authors, journal, publication_year FROM publications WHERE publication_year >= 2022 ORDER BY publication_year DESC LIMIT 5"),
            ("AI research", "SELECT r.name, i.name as institution, g.title FROM researchers r JOIN institutions i ON r.institution_id = i.id JOIN grants g ON g.principal_investigator_id = r.id WHERE g.research_area LIKE '%Artificial Intelligence%'"),
            ("Turkish language research", "SELECT title, authors, journal FROM publications WHERE title LIKE '%Turkish%' OR title LIKE '%Türkçe%'"),
        ]
        
        for query_name, query in test_queries:
            logger.info(f"\n=== {query_name} ===")
            logger.info(f"Query: {query}")
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Get column names
            column_names = [description[0] for description in cursor.description]
            logger.info(f"Columns: {', '.join(column_names)}")
            logger.info(f"Results count: {len(results)}")
            
            # Show first few results
            for i, row in enumerate(results[:3]):
                logger.info(f"Row {i+1}: {dict(zip(column_names, row))}")
            
            if len(results) > 3:
                logger.info(f"... and {len(results) - 3} more rows")
        
        conn.close()
        logger.info("\n✅ AtlasIQ database test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error testing database: {str(e)}")
        return False


if __name__ == "__main__":
    success = test_atlasiq_database()
    if success:
        print("✅ AtlasIQ database test passed!")
    else:
        print("❌ AtlasIQ database test failed!")
        sys.exit(1)
