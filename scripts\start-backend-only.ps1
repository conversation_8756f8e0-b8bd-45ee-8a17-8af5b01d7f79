# Agentic RAG Backend-Only Başlatma Scripti
# Atlas University, Istanbul, Türkiye

param(
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Logs,
    [switch]$Status
)

# Renkli çıktı için fonksiyonlar
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Ana fonksiyonlar
function Start-Backend {
    Write-Info "Agentic RAG Backend başlatılıyor..."
    
    # .env dosyasını kontrol et
    if (-not (Test-Path ".env")) {
        Write-Warning ".env dosyası bulunamadı. .env.example'dan kopyalanıyor..."
        Copy-Item ".env.example" ".env"
        Write-Warning "Lütfen .env dosyasını düzenleyin ve API key'le<PERSON><PERSON> ekleyin"
        return
    }
    
    # MongoDB kontrolü
    Write-Info "MongoDB bağlantısı kontrol ediliyor..."
    try {
        $mongoTest = Test-NetConnection -ComputerName "localhost" -Port 27017 -WarningAction SilentlyContinue
        if (-not $mongoTest.TcpTestSucceeded) {
            Write-Warning "MongoDB localhost:27017'de çalışmıyor"
            Write-Info "MongoDB'yi başlatmak için:"
            Write-Info "  Docker: docker run -d --name mongodb -p 27017:27017 mongo:7.0"
            Write-Info "  Yerel: sudo systemctl start mongod"
        } else {
            Write-Success "MongoDB bağlantısı başarılı"
        }
    } catch {
        Write-Warning "MongoDB bağlantısı kontrol edilemedi"
    }
    
    # Docker Compose ile başlat
    Write-Info "Docker container başlatılıyor..."
    docker-compose -f docker-compose.backend-only.yml up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Backend başarıyla başlatıldı!"
        Write-Info "Erişim adresleri:"
        Write-Info "  • API: http://localhost:8000"
        Write-Info "  • API Docs: http://localhost:8000/docs"
        Write-Info "  • Health Check: http://localhost:8000/"
    } else {
        Write-Error "Backend başlatılamadı!"
    }
}

function Stop-Backend {
    Write-Info "Agentic RAG Backend durduruluyor..."
    docker-compose -f docker-compose.backend-only.yml down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Backend başarıyla durduruldu!"
    } else {
        Write-Error "Backend durdurulamadı!"
    }
}

function Restart-Backend {
    Write-Info "Agentic RAG Backend yeniden başlatılıyor..."
    docker-compose -f docker-compose.backend-only.yml restart
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Backend başarıyla yeniden başlatıldı!"
    } else {
        Write-Error "Backend yeniden başlatılamadı!"
    }
}

function Show-Logs {
    Write-Info "Backend logları gösteriliyor..."
    docker-compose -f docker-compose.backend-only.yml logs -f agentic-rag
}

function Show-Status {
    Write-Info "Backend durumu:"
    docker-compose -f docker-compose.backend-only.yml ps
    
    Write-Info "`nContainer logları (son 10 satır):"
    docker-compose -f docker-compose.backend-only.yml logs --tail=10 agentic-rag
}

# Ana script mantığı
if ($Stop) {
    Stop-Backend
} elseif ($Restart) {
    Restart-Backend
} elseif ($Logs) {
    Show-Logs
} elseif ($Status) {
    Show-Status
} else {
    Start-Backend
}
