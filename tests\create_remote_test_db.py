#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test PostgreSQL database for remote SQL connection testing.
This script creates a PostgreSQL database with sample data for testing the AdminBot.
"""

import os
import sys
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Float, Date, DateTime
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, date

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'username': 'postgres',
    'password': 'admin123',
    'database': 'university_db'
}

def create_connection_string(config):
    """Create PostgreSQL connection string."""
    return f"postgresql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"

def create_database_if_not_exists(config):
    """Create the database if it doesn't exist."""
    # Connect to default postgres database to create our database
    default_config = config.copy()
    default_config['database'] = 'postgres'
    default_conn_str = create_connection_string(default_config)
    
    try:
        engine = create_engine(default_conn_str)
        with engine.connect() as conn:
            # Check if database exists
            result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{config['database']}'"))
            if not result.fetchone():
                # Create database
                conn.execute(text("COMMIT"))  # End any existing transaction
                conn.execute(text(f"CREATE DATABASE {config['database']}"))
                logger.info(f"Database '{config['database']}' created successfully")
            else:
                logger.info(f"Database '{config['database']}' already exists")
    except SQLAlchemyError as e:
        logger.error(f"Error creating database: {str(e)}")
        raise

def create_tables_and_data(connection_string):
    """Create tables and insert sample data."""
    try:
        engine = create_engine(connection_string)
        
        with engine.connect() as conn:
            # Create staff table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS staff (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    department VARCHAR(50) NOT NULL,
                    position VARCHAR(50) NOT NULL,
                    salary DECIMAL(10,2),
                    hire_date DATE,
                    email VARCHAR(100)
                )
            """))
            
            # Create departments table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS departments (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50) NOT NULL,
                    head VARCHAR(100),
                    budget DECIMAL(12,2),
                    location VARCHAR(100)
                )
            """))
            
            # Create budgets table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS budgets (
                    id SERIAL PRIMARY KEY,
                    department VARCHAR(50) NOT NULL,
                    year INTEGER NOT NULL,
                    allocated_amount DECIMAL(12,2),
                    spent_amount DECIMAL(12,2),
                    remaining_amount DECIMAL(12,2)
                )
            """))
            
            # Create facilities table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS facilities (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    type VARCHAR(50),
                    capacity INTEGER,
                    location VARCHAR(100),
                    status VARCHAR(20)
                )
            """))
            
            # Create events table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS events (
                    id SERIAL PRIMARY KEY,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    event_date DATE,
                    location VARCHAR(100),
                    organizer VARCHAR(100)
                )
            """))
            
            # Insert sample data for staff
            conn.execute(text("""
                INSERT INTO staff (name, department, position, salary, hire_date, email) VALUES
                ('Dr. Mehmet Yılmaz', 'Computer Science', 'Professor', 15000.00, '2015-09-01', '<EMAIL>'),
                ('Dr. Ayşe Kaya', 'Mathematics', 'Associate Professor', 12000.00, '2018-02-15', '<EMAIL>'),
                ('Ahmet Demir', 'Engineering', 'Assistant Professor', 10000.00, '2020-08-20', '<EMAIL>'),
                ('Fatma Şahin', 'Business', 'Lecturer', 8000.00, '2019-01-10', '<EMAIL>'),
                ('Can Özkan', 'Computer Science', 'Research Assistant', 5000.00, '2021-09-01', '<EMAIL>')
                ON CONFLICT DO NOTHING
            """))
            
            # Insert sample data for departments
            conn.execute(text("""
                INSERT INTO departments (name, head, budget, location) VALUES
                ('Computer Science', 'Dr. Mehmet Yılmaz', 500000.00, 'Technology Building'),
                ('Mathematics', 'Dr. Ayşe Kaya', 300000.00, 'Science Building'),
                ('Engineering', 'Dr. Ali Veli', 800000.00, 'Engineering Building'),
                ('Business', 'Dr. Zeynep Ak', 400000.00, 'Business Building')
                ON CONFLICT DO NOTHING
            """))
            
            # Insert sample data for budgets
            conn.execute(text("""
                INSERT INTO budgets (department, year, allocated_amount, spent_amount, remaining_amount) VALUES
                ('Computer Science', 2024, 500000.00, 350000.00, 150000.00),
                ('Mathematics', 2024, 300000.00, 200000.00, 100000.00),
                ('Engineering', 2024, 800000.00, 600000.00, 200000.00),
                ('Business', 2024, 400000.00, 250000.00, 150000.00)
                ON CONFLICT DO NOTHING
            """))
            
            # Insert sample data for facilities
            conn.execute(text("""
                INSERT INTO facilities (name, type, capacity, location, status) VALUES
                ('Amphitheater A1', 'Lecture Hall', 200, 'Main Building', 'Active'),
                ('Computer Lab 1', 'Laboratory', 30, 'Technology Building', 'Active'),
                ('Library Main Hall', 'Library', 500, 'Library Building', 'Active'),
                ('Conference Room B2', 'Meeting Room', 50, 'Administration Building', 'Active'),
                ('Sports Hall', 'Gymnasium', 1000, 'Sports Complex', 'Under Maintenance')
                ON CONFLICT DO NOTHING
            """))
            
            # Insert sample data for events
            conn.execute(text("""
                INSERT INTO events (title, description, event_date, location, organizer) VALUES
                ('AI Conference 2024', 'Annual Artificial Intelligence Conference', '2024-06-15', 'Amphitheater A1', 'Dr. Mehmet Yılmaz'),
                ('Mathematics Symposium', 'International Mathematics Symposium', '2024-07-20', 'Conference Room B2', 'Dr. Ayşe Kaya'),
                ('Engineering Fair', 'Student Engineering Projects Fair', '2024-05-10', 'Engineering Building', 'Dr. Ali Veli'),
                ('Business Workshop', 'Entrepreneurship Workshop', '2024-04-25', 'Business Building', 'Dr. Zeynep Ak')
                ON CONFLICT DO NOTHING
            """))
            
            conn.commit()
            logger.info("Tables created and sample data inserted successfully")
            
    except SQLAlchemyError as e:
        logger.error(f"Error creating tables and data: {str(e)}")
        raise

def test_connection(connection_string):
    """Test the database connection and query some data."""
    try:
        engine = create_engine(connection_string)
        with engine.connect() as conn:
            # Test query
            result = conn.execute(text("SELECT COUNT(*) as staff_count FROM staff"))
            count = result.fetchone()[0]
            logger.info(f"Connection test successful. Staff count: {count}")
            
            # Show sample data
            result = conn.execute(text("SELECT name, department, position FROM staff LIMIT 3"))
            logger.info("Sample staff data:")
            for row in result:
                logger.info(f"  - {row[0]} ({row[1]}, {row[2]})")
                
    except SQLAlchemyError as e:
        logger.error(f"Connection test failed: {str(e)}")
        raise

def main():
    """Main function to set up the test database."""
    logger.info("Setting up PostgreSQL test database for AdminBot...")
    
    try:
        # Create database if it doesn't exist
        create_database_if_not_exists(DB_CONFIG)
        
        # Create connection string for our database
        connection_string = create_connection_string(DB_CONFIG)
        
        # Create tables and insert data
        create_tables_and_data(connection_string)
        
        # Test connection
        test_connection(connection_string)
        
        logger.info("Database setup completed successfully!")
        logger.info(f"Connection string: {connection_string}")
        logger.info("You can now update your admin_bot.yaml config file with this connection string.")
        
    except Exception as e:
        logger.error(f"Database setup failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
