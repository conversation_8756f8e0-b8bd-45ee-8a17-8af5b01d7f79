# Chapter 3: Query Router

Welcome back! In the previous chapters, we learned about the [AgenticRAG System](01_agenticrag_system_.md) as the central manager and the [<PERSON><PERSON>](02_bot_.md) as a specialized assistant with its own tools and purpose.

Now, let's dive *inside* a specific Bot. When a Bot receives a user's question, how does it decide what to do? Does it immediately look for documents? Does it search the web? Or can it answer without needing any external tools? This crucial decision-maker inside the Bot is the **Query Router**.

## What Problem Does the Query Router Solve?

Imagine our "AcademicBot" receives the question: "Tell me about recent advancements in machine learning."

This question *might* require searching external documents or the web. But what if the question is "Hello, AcademicBot!"? This is a simple greeting and doesn't need a complex tool search.

The problem is: **How does a Bot intelligently decide if and how to use its specialized tools for a given user query?**

This is where the **Query Router** shines. It's like a smart receptionist for the Bot. When a query comes in, the Query Router reads it, understands the user's intent, and decides which "department" (or [Tool](04_tool_.md)) within the Bot is best equipped to handle the request.

Its main job is to figure out:

1.  Does this query require using any of the Bot's [Tool](04_tool_.md)s?
2.  If yes, which specific [Tool](04_tool_.md)s are needed?
3.  (Optionally) Execute the selected tools and gather their results.

It then passes this information (the original query plus any results from executed tools) to the Bot's brain ([Agent (LangGraphAgent)](05_agent__langgraphagent__.md)) for crafting the final answer.

## How Does It Work? (High-Level)

The Query Router uses the power of an AI model (specifically, an LLM - Large Language Model) to help make routing decisions. It takes the user's query and the descriptions of the tools available to the Bot, and asks the LLM: "Given this query and these tools, which tools should I use?"

The LLM analyzes the query and the tool descriptions and suggests one or more tools. The Query Router then takes this suggestion, potentially executes those tools, and prepares the results.

It also has a fallback mechanism, like looking for specific keywords in the query that are associated with certain tools.

## Where Does the Query Router Fit in the Flow?

Let's revisit the flow we saw in Chapter 1, but now zooming in on the Query Router's place:

```mermaid
sequenceDiagram
    participant User
    participant FastAPIA as FastAPI App
    participant AgenticRAG as AgenticRAG System
    participant BotC as Bot's Components
    participant QueryRouter as Query Router
    participant Tool as Tool
    participant Agent as Agent

    User->>FastAPIA: Query Request
    FastAPIA->>AgenticRAG: process_query(...)
    AgenticRAG->>BotC: Get specific Bot's parts
    AgenticRAG->>QueryRouter: route_query(user_query, ...)
    QueryRouter->>QueryRouter: Decides which tools to use
    alt Tools Selected
        QueryRouter->>Tool: Execute selected Tool(s)
        Tool-->>QueryRouter: Tool Results
    end
    QueryRouter-->>AgenticRAG: Tool Selection & Results
    AgenticRAG->>Agent: process_query(user_query, tool_results, ...)
    Agent-->>AgenticRAG: Final response text
    AgenticRAG->>FastAPIA: QueryResponse
    FastAPIA->>User: JSON Response
```

As you can see, after the [AgenticRAG System](01_agenticrag_system_.md) gets the right Bot components, the user's query first goes to the **Query Router** (`route_query` call). The Router does its decision-making, interacts with the [Tool](04_tool_.md)s if necessary, and then passes the baton (along with any tool results) back to the [AgenticRAG System](01_agenticrag_system_.md), which then calls the Bot's [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).

## Using the Query Router (Code Snippet)

You don't directly call the Query Router from outside the system. The [AgenticRAG System](01_agenticrag_system_.md) handles this call as part of processing a query for a specific [Bot](02_bot_.md).

Let's look at the relevant part in `app/core/agentic_rag.py`:

```python
# File: app\core\agentic_rag.py (simplified)

    async def process_query(
        self, bot_name: str, request: QueryRequest
    ) -> QueryResponse:
        # ... get the bot components ...

        try:
            # 1. Route the query using the bot's query router
            query_router: QueryRouter = bot["query_router"] # Get the router instance
            tool_results = await query_router.route_query( # Call its route_query method
                request.query, **request.metadata or {}
            )

            # 2. Process the query and tool results with the bot's agent
            agent: LangGraphAgent = bot["agent"]
            agent_response = await agent.process_query(
                request.query,
                tool_results["tool_responses"], # Pass results from router
                session_id=request.session_id,
            )

            # ... format and return response ...
```

**Explanation:**

*   `query_router = bot["query_router"]`: The code retrieves the specific `QueryRouter` instance that was initialized for this particular `bot_name` when the system started.
*   `tool_results = await query_router.route_query(...)`: This is where the magic happens! The user's query (`request.query`) is passed to the router's `route_query` method. The `await` keyword means the program will pause here until the `route_query` method finishes its work (which might involve calling asynchronous tools).
*   `tool_results`: This variable will hold the output from the router. It's a dictionary that includes which tools were *selected* and the actual *responses* obtained from executing those tools.
*   `agent.process_query(...)`: The results from the router (`tool_results["tool_responses"]`) are then passed along with the original query to the Bot's [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).

So, the `route_query` method is the main entry point into the Query Router's logic from the perspective of the rest of the system.

## Under the Hood: How the Query Router Decides

Let's peek into the `app/core/query_router.py` file to see how the `route_query` method works internally.

### The `route_query` Method

```python
# File: app\core\query_router.py (simplified)

class QueryRouter:
    def __init__(self, bot_config: BotConfig, tools: Dict[str, BaseTool]):
        # ... initialization ...
        self.tools = tools # Router has access to the bot's tools
        # Initialize the LLM used for tool selection
        self.llm = ChatOpenAI(model=bot_config.agent.model, temperature=0.0)

    async def route_query(self, query: str, **kwargs) -> Dict[str, Any]:
        # 1. Use the LLM to determine which tools to use and why
        tool_selection_result = await self._select_tools_with_reasoning(query)
        tools_to_use = tool_selection_result["selected_tools"]
        logger.info(f"Selected tools: {tools_to_use}")
        logger.info(f"Reasoning: {tool_selection_result.get('reasoning')}")

        # 2. Execute each tool that was selected
        tool_responses = await self._execute_tools(tools_to_use, query, **kwargs)

        # 3. Return the results
        return {
            "query": query,
            "tool_responses": tool_responses,
            "selected_tools": tools_to_use,
            # ... include reasoning and raw LLM output ...
        }
```

**Explanation:**

1.  **`_select_tools_with_reasoning(query)`**: This is the core decision-making step. This method uses the internal LLM (`self.llm`) to figure out which tools are relevant. It returns a dictionary indicating the `selected_tools` and the `reasoning` behind the LLM's choice.
2.  **`_execute_tools(tools_to_use, query, **kwargs)`**: Based on the list of `tools_to_use` returned by the selection step, this method actually runs each of the selected tools using the original `query`. It gathers the results into a dictionary `tool_responses`.
3.  **Return Results**: Finally, `route_query` bundles up all the information: the original query, the responses from the tools, the list of selected tools, and the LLM's reasoning. This dictionary is then returned to the [AgenticRAG System](01_agenticrag_system_.md).

Let's look briefly at how the selection and execution steps work.

### Step 1: Selecting Tools (`_select_tools_with_reasoning`)

This method prepares a prompt for the LLM, asking it to choose tools. It provides the user's query and a list of available tools with their descriptions.

```python
# File: app\core\query_router.py (simplified)

    async def _select_tools_with_reasoning(self, query: str) -> Dict[str, Any]:
        # Get descriptions of available tools for THIS bot
        tool_descriptions = self._get_tool_descriptions()

        if not tool_descriptions:
            # No tools available, nothing to select
            return {"selected_tools": [], "reasoning": "No tools available."}

        # Prepare the prompt for the LLM
        messages = self._get_tool_selection_prompt(query, tool_descriptions)

        try:
            # Ask the LLM which tools to use
            response = self.llm.invoke(messages)
            response_text = response.content.strip()

            # Parse the LLM's response (expects JSON)
            # Includes fallback logic if JSON parsing fails
            return self._parse_llm_response(response_text, self._get_keyword_selected_tools(query))

        except Exception as e:
             # Handle errors, potentially fall back to keyword selection
            # ... error handling ...
            return self._get_fallback_tools(self._get_keyword_selected_tools(query)) # Simplified fallback
```

**Explanation:**

*   `self._get_tool_descriptions()`: This method looks at the tools *configured and enabled* for this specific Bot and gets their descriptions.
*   `self._get_tool_selection_prompt(...)`: This method builds the actual text prompt sent to the LLM. It includes instructions (in a System Message) and the query/tool info (in a Human Message).
*   `self.llm.invoke(messages)`: This sends the prompt to the LLM and gets its response.
*   `self._parse_llm_response(...)`: This method takes the LLM's response (which should ideally be a JSON string) and extracts the list of selected tool names and the reasoning. It also includes important fallback logic: if the LLM response is not valid or doesn't list tools, it can use keyword matching (`self._get_keyword_selected_tools(query)`) or even just return all enabled tools as a default.

This is where the AI makes the smart routing decision based on the query's meaning and the tools' capabilities.

### Step 2: Executing Tools (`_execute_tools`)

Once the list of tools to use is determined, this method simply runs each one.

```python
# File: app\core\query_router.py (simplified)

    async def _execute_tools(
        self, tool_names: List[str], query: str, **kwargs
    ) -> Dict[str, Any]:
        tool_responses = {}

        for tool_name in tool_names:
            if tool_name in self.tools: # Check if the tool exists for this bot
                try:
                    tool = self.tools[tool_name] # Get the actual tool instance
                    response = await tool.execute(query, **kwargs) # Run the tool!
                    tool_responses[tool_name] = response
                except Exception as e:
                    # Handle errors for a specific tool execution
                    logger.error(f"Error executing tool {tool_name}: {str(e)}")
                    tool_responses[tool_name] = {"success": False, "error": str(e)}
            else:
                # Log a warning if a selected tool isn't available (shouldn't happen often)
                logger.warning(f"Tool {tool_name} not found")

        return tool_responses
```

**Explanation:**

*   The method loops through the list of `tool_names` provided.
*   `if tool_name in self.tools:`: It checks if the tool name is actually one of the tools initialized for this specific Bot.
*   `tool = self.tools[tool_name]`: It retrieves the actual instance of the [Tool](04_tool_.md) class.
*   `response = await tool.execute(query, **kwargs)`: It calls the `execute` method on that specific tool instance, passing the original `query` and any extra information (`**kwargs`). The `await` here is important because tool execution (like searching the web or a database) can take time.
*   The response from each tool is stored in the `tool_responses` dictionary, keyed by the tool's name. Error handling is included so that if one tool fails, the others can still run.

This method ensures that all the tools the Router decided were necessary are run and their outputs collected before being sent to the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).

## Conclusion

In this chapter, we explored the **Query Router**, a key component *within* each [Bot](02_bot_.md). We learned that it acts as the Bot's intelligent receptionist, receiving user queries and deciding whether and which of the Bot's specialized [Tool](04_tool_.md)s are needed to answer the question. It typically uses an AI model (LLM) for this decision-making process, comparing the query against tool descriptions, and includes fallback mechanisms like keyword matching. It then executes the selected tools and bundles their results along with the selection information before returning it to the [AgenticRAG System](01_agenticrag_system_.md) for the final response generation by the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).

Now that we understand how the system decides *when* and *which* tools to use, let's look at what those tools actually *are*. In the next chapter, we'll dive into the concept of a **Tool**.

[Tool](04_tool_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)