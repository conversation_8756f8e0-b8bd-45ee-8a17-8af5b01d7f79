-- Initialize PostgreSQL database for Atlas University AdminBot testing
-- This script creates tables and inserts sample data

-- Create staff table
CREATE TABLE IF NOT EXISTS staff (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    department VARCHAR(50) NOT NULL,
    position VARCHAR(50) NOT NULL,
    salary DECIMAL(10,2),
    hire_date DATE,
    email VARCHAR(100)
);

-- Create departments table
CREATE TABLE IF NOT EXISTS departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    head VARCHAR(100),
    budget DECIMAL(12,2),
    location VARCHAR(100)
);

-- Create budgets table
CREATE TABLE IF NOT EXISTS budgets (
    id SERIAL PRIMARY KEY,
    department VARCHAR(50) NOT NULL,
    year INTEGER NOT NULL,
    allocated_amount DECIMAL(12,2),
    spent_amount DECIMAL(12,2),
    remaining_amount DECIMAL(12,2)
);

-- Create facilities table
CREATE TABLE IF NOT EXISTS facilities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50),
    capacity INTEGER,
    location VARCHAR(100),
    status VARCHAR(20)
);

-- Create events table
CREATE TABLE IF NOT EXISTS events (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE,
    location VARCHAR(100),
    organizer VARCHAR(100)
);

-- Insert sample data for staff
INSERT INTO staff (name, department, position, salary, hire_date, email) VALUES
('Dr. Mehmet Yılmaz', 'Computer Science', 'Professor', 15000.00, '2015-09-01', '<EMAIL>'),
('Dr. Ayşe Kaya', 'Mathematics', 'Associate Professor', 12000.00, '2018-02-15', '<EMAIL>'),
('Ahmet Demir', 'Engineering', 'Assistant Professor', 10000.00, '2020-08-20', '<EMAIL>'),
('Fatma Şahin', 'Business', 'Lecturer', 8000.00, '2019-01-10', '<EMAIL>'),
('Can Özkan', 'Computer Science', 'Research Assistant', 5000.00, '2021-09-01', '<EMAIL>'),
('Dr. Zeynep Ak', 'Business', 'Professor', 14000.00, '2016-03-01', '<EMAIL>'),
('Dr. Ali Veli', 'Engineering', 'Professor', 16000.00, '2014-01-15', '<EMAIL>'),
('Elif Yıldız', 'Mathematics', 'Research Assistant', 4500.00, '2022-09-01', '<EMAIL>');

-- Insert sample data for departments
INSERT INTO departments (name, head, budget, location) VALUES
('Computer Science', 'Dr. Mehmet Yılmaz', 500000.00, 'Technology Building'),
('Mathematics', 'Dr. Ayşe Kaya', 300000.00, 'Science Building'),
('Engineering', 'Dr. Ali Veli', 800000.00, 'Engineering Building'),
('Business', 'Dr. Zeynep Ak', 400000.00, 'Business Building');

-- Insert sample data for budgets
INSERT INTO budgets (department, year, allocated_amount, spent_amount, remaining_amount) VALUES
('Computer Science', 2024, 500000.00, 350000.00, 150000.00),
('Mathematics', 2024, 300000.00, 200000.00, 100000.00),
('Engineering', 2024, 800000.00, 600000.00, 200000.00),
('Business', 2024, 400000.00, 250000.00, 150000.00),
('Computer Science', 2023, 450000.00, 445000.00, 5000.00),
('Mathematics', 2023, 280000.00, 275000.00, 5000.00),
('Engineering', 2023, 750000.00, 740000.00, 10000.00),
('Business', 2023, 380000.00, 370000.00, 10000.00);

-- Insert sample data for facilities
INSERT INTO facilities (name, type, capacity, location, status) VALUES
('Amphitheater A1', 'Lecture Hall', 200, 'Main Building', 'Active'),
('Amphitheater A2', 'Lecture Hall', 150, 'Main Building', 'Active'),
('Computer Lab 1', 'Laboratory', 30, 'Technology Building', 'Active'),
('Computer Lab 2', 'Laboratory', 25, 'Technology Building', 'Active'),
('Physics Lab', 'Laboratory', 20, 'Science Building', 'Active'),
('Chemistry Lab', 'Laboratory', 25, 'Science Building', 'Under Maintenance'),
('Library Main Hall', 'Library', 500, 'Library Building', 'Active'),
('Library Study Rooms', 'Study Area', 100, 'Library Building', 'Active'),
('Conference Room B1', 'Meeting Room', 30, 'Administration Building', 'Active'),
('Conference Room B2', 'Meeting Room', 50, 'Administration Building', 'Active'),
('Sports Hall', 'Gymnasium', 1000, 'Sports Complex', 'Under Maintenance'),
('Swimming Pool', 'Sports Facility', 50, 'Sports Complex', 'Active');

-- Insert sample data for events
INSERT INTO events (title, description, event_date, location, organizer) VALUES
('AI Conference 2024', 'Annual Artificial Intelligence Conference', '2024-06-15', 'Amphitheater A1', 'Dr. Mehmet Yılmaz'),
('Mathematics Symposium', 'International Mathematics Symposium', '2024-07-20', 'Conference Room B2', 'Dr. Ayşe Kaya'),
('Engineering Fair', 'Student Engineering Projects Fair', '2024-05-10', 'Engineering Building', 'Dr. Ali Veli'),
('Business Workshop', 'Entrepreneurship Workshop', '2024-04-25', 'Business Building', 'Dr. Zeynep Ak'),
('Research Presentation Day', 'Annual Research Presentations', '2024-12-15', 'Amphitheater A1', 'Dr. Mehmet Yılmaz'),
('Career Fair', 'Annual Career and Job Fair', '2024-10-20', 'Sports Hall', 'Career Center'),
('Graduation Ceremony', 'Spring 2024 Graduation', '2024-06-30', 'Sports Hall', 'Administration'),
('New Student Orientation', 'Orientation for New Students', '2024-09-01', 'Amphitheater A1', 'Student Affairs');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department);
CREATE INDEX IF NOT EXISTS idx_budgets_department_year ON budgets(department, year);
CREATE INDEX IF NOT EXISTS idx_events_date ON events(event_date);
CREATE INDEX IF NOT EXISTS idx_facilities_status ON facilities(status);

-- Grant permissions (if needed)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
