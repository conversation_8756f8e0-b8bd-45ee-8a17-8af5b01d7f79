import pyodbc

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=10.1.1.83;"
    "DATABASE=VERSISDB;"  # doğru ad
    "UID=ai-read;"
    "PWD=CHXG7jS2y9X4;"
    "TrustServerCertificate=yes;"
)

conn = pyodbc.connect(conn_str)
cursor = conn.cursor()

cursor.execute(
    """
    SELECT TABLE_NAME 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_TYPE = 'BASE TABLE'
"""
)

print("Tablolar:")
for row in cursor.fetchall():
    print("-", row.TABLE_NAME)
